using Contract.CapTable;

namespace ManagedAccounts.Models.Results
{
    /// <summary>
    /// Result for managed cap table values query
    /// </summary>
    public class GetManagedCapTableValuesResult
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// The cap table response data
        /// </summary>
        public PcCapTableResponse? CapTableData { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        /// <param name="capTableData">The cap table response data</param>
        /// <returns>Successful result</returns>
        public static GetManagedCapTableValuesResult Success(PcCapTableResponse capTableData)
        {
            return new GetManagedCapTableValuesResult
            {
                IsSuccess = true,
                CapTableData = capTableData
            };
        }

        /// <summary>
        /// Creates a failed result
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <returns>Failed result</returns>
        public static GetManagedCapTableValuesResult Failure(string errorMessage)
        {
            return new GetManagedCapTableValuesResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }
}
