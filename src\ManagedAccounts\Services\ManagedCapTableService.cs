using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Azure.Core;
using Contract.CapTable;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.UnitOfWork;
using ManagedAccounts.Helpers;
using ManagedAccounts.Interfaces;
using ManagedAccounts.Models.Results;
using Microsoft.Extensions.Logging;
using PortfolioCompany.API.Helpers;

namespace ManagedAccounts.Services
{
    /// <summary>
    /// Service implementation for managed account cap table operations
    /// </summary>
    public class ManagedCapTableService : IManagedCapTableService
    {
        private readonly ILogger<ManagedCapTableService> _logger;
        private readonly IDapperGenericRepository _dapperRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IManagedAccountDetailsService _managedAccountService;

        public ManagedCapTableService(
            ILogger<ManagedCapTableService> logger,
            IDapperGenericRepository dapperRepository,
            IManagedAccountDetailsService managedAccountDetailsService,
            IUnitOfWork unitOfWork)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dapperRepository = dapperRepository ?? throw new ArgumentNullException(nameof(dapperRepository));
            _managedAccountService = managedAccountDetailsService;
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        /// <summary>
        /// Gets the cap table configuration for a managed account
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="moduleName">The module name</param>
        /// <returns>The cap table configuration</returns>
        public async Task<ManagedCapTableConfigResponse> GetCapTableConfigAsync(Guid managedAccountId, string moduleName)
        {
            try
            {
                _logger.LogInformation("Getting cap table configuration for managed account: {ManagedAccountId}, module: {ModuleName}", 
                    managedAccountId, moduleName);

                // Validate input parameters
                var validationError = ManagedCapTableHelper.ValidateCapTableConfigQuery(managedAccountId, moduleName);
                if (!string.IsNullOrEmpty(validationError))
                {
                    _logger.LogWarning("Validation failed: {ValidationError}", validationError);
                    throw new ArgumentException(validationError);
                }

                // Get portfolio company ID from managed account ID
                var portfolioCompanyId = await GetPortfolioCompanyIdFromManagedAccountAsync(managedAccountId);
                if (portfolioCompanyId == 0)
                {
                    _logger.LogWarning("No portfolio company found for managed account: {ManagedAccountId}", managedAccountId);
                    return new ManagedCapTableConfigResponse();
                }

                // Get cap table periods for the portfolio company
                var capTablePeriods = await _dapperRepository.Query<CapTablePeriod>(
                    SqlConstants.QueryByCapTableConfig, 
                    new { companyId = portfolioCompanyId });

                _logger.LogInformation("Retrieved {Count} cap table periods for portfolio company: {PortfolioCompanyId}", 
                    capTablePeriods.Count(), portfolioCompanyId);

                // Create and return the configuration response
                return ManagedCapTableHelper.CreateConfigResponse(capTablePeriods.ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cap table configuration for managed account: {ManagedAccountId}", managedAccountId);
                throw;
            }
        }

        /// <summary>
        /// Gets the cap table values for a managed account
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="periodId">The period ID</param>
        /// <param name="isMonthly">Whether to include monthly data</param>
        /// <param name="isQuarterly">Whether to include quarterly data</param>
        /// <param name="isAnnually">Whether to include annually data</param>
        /// <param name="moduleName">The module name</param>
        /// <returns>The cap table values</returns>
        public async Task<PcCapTableResponse> GetCapTableValuesAsync(
            Guid managedAccountId, 
            int periodId, 
            bool isMonthly, 
            bool isQuarterly, 
            bool isAnnually, 
            string moduleName)
        {
            try
            {
                _logger.LogInformation("Getting cap table values for managed account: {ManagedAccountId}, period: {PeriodId}, module: {ModuleName}", 
                    managedAccountId, periodId, moduleName);

                // Validate input parameters
                var validationError = ManagedCapTableHelper.ValidateCapTableValuesQuery(managedAccountId, periodId, moduleName);
                if (!string.IsNullOrEmpty(validationError))
                {
                    _logger.LogWarning("Validation failed: {ValidationError}", validationError);
                    throw new ArgumentException(validationError);
                }

                // Get portfolio company ID from managed account ID
                var portfolioCompanyId = await GetPortfolioCompanyIdFromManagedAccountAsync(managedAccountId);
                if (portfolioCompanyId == 0)
                {
                    _logger.LogWarning("No portfolio company found for managed account: {ManagedAccountId}", managedAccountId);
                    return new PcCapTableResponse();
                }

                // Get module ID from module name
                var moduleId = await GetModuleIdFromNameAsync(moduleName);
                if (moduleId == 0)
                {
                    _logger.LogWarning("No module found for module name: {ModuleName}", moduleName);
                    return new PcCapTableResponse();
                }

                // Create filter for cap table values query
                var filter = new PcCapTableFilterType
                {
                    CompanyId = portfolioCompanyId,
                    PeriodId = periodId,
                    ModuleId = moduleId,
                    IsMonthly = isMonthly,
                    IsQuarterly = isQuarterly,
                    IsAnnually = isAnnually
                };

                // Get cap table data using the same logic as CapTableService
                var (capTableValues, capTableColumns, headers) = await GetCapTableDataAsync(filter);

                // Create and populate the response
                var response = new PcCapTableResponse 
                { 
                    Headers = headers,
                    IsMonthly = isMonthly,
                    IsQuarterly = isQuarterly,
                    IsAnnually = isAnnually
                };

                PcCapTableHelper.CreateDynamicObjects(response, capTableValues.OrderBy(x => x.DisplayOrder).ToList(), capTableColumns);

                _logger.LogInformation("Successfully retrieved cap table values for managed account: {ManagedAccountId}", managedAccountId);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cap table values for managed account: {ManagedAccountId}", managedAccountId);
                throw;
            }
        }

        /// <summary>
        /// Gets portfolio company ID from managed account ID
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <returns>The portfolio company ID</returns>
        private async Task<int> GetPortfolioCompanyIdFromManagedAccountAsync(Guid managedAccountId)
        {
            try
            {
                // Query the ManagedAccountDetails table using Dapper
                var managedAccount = await _managedAccountService.GetByIdAsync(managedAccountId);

                if (managedAccount != null)
                {
                    return managedAccount.UAMId; // in ManangedCaptable PCCompnyId is equals to UAMid for managedaccunts
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting portfolio company ID for managed account: {ManagedAccountId}", managedAccountId);
                return 0;
            }
        }

        /// <summary>
        /// Gets module ID from module name
        /// </summary>
        /// <param name="moduleName">The module name</param>
        /// <returns>The module ID</returns>
        private async Task<int> GetModuleIdFromNameAsync(string moduleName)
        {
            try
            {
                // TODO: Implement actual module name to ID mapping
                // This should query the appropriate table to get ModuleId from ModuleName
                // For now, returning a placeholder value
                await Task.CompletedTask; // Remove this when implementing actual logic
                return 1; // Placeholder
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting module ID for module name: {ModuleName}", moduleName);
                return 0;
            }
        }

        /// <summary>
        /// Gets cap table data similar to CapTableService.GetCapTableData
        /// </summary>
        /// <param name="filter">The cap table filter</param>
        /// <returns>Cap table values, columns, and headers</returns>
        private async Task<(List<CapTableValue>, List<CapTableColumnModel>, List<CapTableHeader>)> GetCapTableDataAsync(PcCapTableFilterType filter)
        {
            try
            {
                _logger.LogInformation("Retrieving cap table data for company: {CompanyId}, module: {ModuleId}, period: {PeriodId}", 
                    filter.CompanyId, filter.ModuleId, filter.PeriodId);

                using var capTableValuesModel = await _dapperRepository.QueryMultipleDataAsync(
                    SqlConstants.QueryByCapTableValues, 
                    new { 
                        companyId = filter.CompanyId, 
                        moduleId = filter.ModuleId, 
                        periodId = filter.PeriodId 
                    });

                var capTableValues = (await capTableValuesModel.ReadAsync<CapTableValue>()).ToList();
                var capTableColumns = (await capTableValuesModel.ReadAsync<CapTableColumnModel>()).ToList();
                var parentModels = (await capTableValuesModel.ReadAsync<CapTableParentModel>()).ToList();

                // Create headers from the column models
                var headers = PcCapTableHelper.CreateHeadersFromColumns(capTableColumns);
                
                // Add headers from the distinct periods in the cap table values
                headers.AddRange(PcCapTableHelper.CreateHeadersFromDistinctPeriods(capTableValues));
                
                // Add parent KPI information
                PcCapTableHelper.AddParentKpi(capTableValues, parentModels, headers);

                return (capTableValues, capTableColumns, headers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cap table data");
                throw;
            }
        }
    }
}
