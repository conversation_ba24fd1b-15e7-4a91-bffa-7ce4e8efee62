using System.Collections.Generic;
using System.Linq;
using ClosedXML.Excel;
using Contract.BulkUpload;
using Contract.KPI;
using Contract.PortfolioCompany;
using Contract.Utility;
using DataAccessLayer.Models.PageSettings;
using Imports.Models;
using Shared;

namespace Imports.Helpers
{
    public static class CapTableHelper
    {

        /// <summary>
        /// Sets errors to the statuses list if there are any in the excelCodeStatuses list.
        /// </summary>
        /// <param name="statuses">The list of statuses to add errors to.</param>
        /// <param name="excelCodeStatuses">The list of excel code statuses to check for errors.</param>
        /// <param name="workSheet">The worksheet where the errors occurred.</param>
        public static void SetErrors(List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet workSheet)
        {
            // Check if the lists are not null and if there are any errors in the excelCodeStatuses list
            if (statuses != null && excelCodeStatuses != null && excelCodeStatuses.Any())
            {
                // Add a new status with the errors to the statuses list
                statuses.Add(new Status()
                {
                    Message = MessageConstants.FileMessageError,
                    Code = Constants.error,
                    excelStatuses = new List<ExcelStatus>()
                    {
                        new ExcelStatus()
                        {
                            SheetName = workSheet.Name,
                            ErrorCount = excelCodeStatuses.Count,
                            ExcelCodeStatus = excelCodeStatuses
                        }
                    }
                });
            }
        }
        /// <summary>
        /// Retrieves the Cap Table period header information from the specified worksheet.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="worksheet">The worksheet containing the Cap Table data.</param>
        /// <returns>A list of <see cref="BulkUploadHeaderModel"/> objects representing the Cap Table period header.</returns>
        public static List<BulkUploadHeaderModel> GetCapTablePeriodHeader(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet)
        {
            string periodValue = worksheet.Cell(2, 2).GetValue<string>().Trim();
            string address = worksheet.Cell(2, 2).Address.ToString();
            return
            [
                new()
                {
                    Address = address,
                    RowNumber = 2,
                    ColumnNumber = 2,
                    HeaderValue = periodValue,
                    ValueTypeId = (int)ReportValueTypes.Actual,
                    Year = BulkUploadHelper.GetYear(excelCodeStatuses, periodValue, address),
                    Quarter = BulkUploadHelper.GetQuarter(periodValue),
                    Month = BulkUploadHelper.GetMonth(periodValue),
                }
            ];
        }
        /// <summary>
        /// Adds a new Cap Table period to the specified collections based on the provided parameters.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="pcKpiValueModels">The list of PC KPI value models.</param>
        /// <param name="pcCapTableUploadModels">The list of PC Cap Table upload models.</param>
        /// <param name="workSheet">The Excel worksheet.</param>
        /// <param name="moduleId">The module ID.</param>
        public static void AddCapTablePeriod(List<ExcelCodeStatus> excelCodeStatuses, List<PcKpiValueModel> pcKpiValueModels, List<PcCapTableUploadModel> pcCapTableUploadModels, IXLWorksheet workSheet, int moduleId)
        {
            if (pcKpiValueModels is { Count: > 0 } && workSheet != null && pcCapTableUploadModels != null)
            {
                string periodValue = workSheet.Cell(2, 2).GetValue<string>().Trim();
                string address = workSheet.Cell(2, 2).Address.ToString();

                pcCapTableUploadModels.Add(new PcCapTableUploadModel()
                {
                    Quarter = BulkUploadHelper.GetQuarter(periodValue),
                    Year = BulkUploadHelper.GetYear(excelCodeStatuses, periodValue, address),
                    Month = BulkUploadHelper.GetMonth(periodValue),
                    ModuleId = moduleId
                });
            }
        }
        /// <summary>
        /// Adds the values from the excelKpiValues list to the pcKpiValueModels list.
        /// </summary>
        /// <param name="pcKpiValueModels">The list of PC KPI values.</param>
        /// <param name="excelKpiValues">The list of excel KPI values.</param>
        public static void AddKpiValues(List<PcKpiValueModel> pcKpiValueModels, List<PcKpiValueModel> excelKpiValues)
        {
            if (excelKpiValues.Count != 0)
            {
                pcKpiValueModels.AddRange(excelKpiValues);
            }
        }
        /// <summary>
        /// Processes the cap table rows based on the provided parameters.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="pcKpiValueModels">The list of PC KPI value models.</param>
        /// <param name="workSheet">The Excel worksheet.</param>
        /// <param name="moduleId">The module ID.</param>
        /// <param name="kpiModels">The list of FoFKpiModels.</param>
        /// <param name="bulkUploadHeaders">The list of bulk upload header models.</param>
        /// <param name="rowUsed">The list of used Excel rows.</param>
        /// <param name="usedCapTableColumns">The list of used cap table columns.</param>
        /// <returns>The list of PC KPI value models.</returns>
        public static List<PcKpiValueModel> ProcessCapTableRows(BulkUploadDataModel bulkUploadDataModel, List<ExcelCodeStatus> excelCodeStatuses, List<PcKpiValueModel> pcKpiValueModels, IXLWorksheet workSheet, int moduleId, List<FoFKpiModel> kpiModels, List<BulkUploadHeaderModel> bulkUploadHeaders, List<IXLRow> rowUsed, List<int> usedCapTableColumns)
        {
            int startingColumn = usedCapTableColumns.Count == 0 ? 2 : usedCapTableColumns.Last();
            var usedValues = GetCapTableFieldValueList(workSheet, rowUsed, excelCodeStatuses, startingColumn);
            var kpiDataList = BulkUploadHelper.GetKpiDataList(kpiModels.Where(x => x.KpiTypeId == 1).ToList(), usedValues, bulkUploadHeaders.Where(x => x.ColumnNumber > startingColumn).ToList(), bulkUploadDataModel.PortfolioCompanyID, moduleId);
            List<PcKpiValueModel> pcKpiValues = BulkUploadHelper.ProcessKpiRecordsByRow(excelCodeStatuses, kpiDataList);
            return pcKpiValues;
        }
        /// <summary>
        /// Processes the cap table columns based on the provided parameters.
        /// </summary>
        /// <param name="bulkUploadPeriodHeader">The list of bulk upload period headers.</param>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="excelCodeStatuses">The list of excel code statuses.</param>
        /// <param name="workSheet">The IXLWorksheet object representing the worksheet.</param>
        /// <param name="moduleId">The module ID.</param>
        /// <param name="kpiModels">The list of FoFKpiModels.</param>
        /// <param name="rowUsed">The list of IXLRows representing the used rows.</param>
        /// <param name="usedCapTableColumns">The list of used cap table columns.</param>
        /// <returns>The list of PcKpiValueModels.</returns>
        public static List<PcKpiValueModel> ProcessCapTableColumns(List<BulkUploadHeaderModel> bulkUploadPeriodHeader, BulkUploadDataModel bulkUploadDataModel, List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet workSheet, int moduleId, List<FoFKpiModel> kpiModels, List<IXLRow> rowUsed, List<int> usedCapTableColumns)
        {
            GetStartAndEndColumn(usedCapTableColumns, out int startingCapColumn, out int endCapColumn);
            if (startingCapColumn > 0 && endCapColumn > 0)
            {
                string periodValue = workSheet.Cell(2, 2).GetValue<string>().Trim();
                var usedColumnValues = GetCapTableColumnFieldValueList(workSheet, rowUsed, excelCodeStatuses, startingCapColumn, endCapColumn);
                var kpiDataColumnList = BulkUploadHelper.GetCapTableColumnKpiDataList(kpiModels, usedColumnValues, bulkUploadPeriodHeader.FirstOrDefault(), bulkUploadDataModel.PortfolioCompanyID, moduleId);
                List<PcKpiValueModel> pcKpiColumnValues = BulkUploadHelper.ProcessKpiRecordsByRow(excelCodeStatuses, kpiDataColumnList);
                return pcKpiColumnValues;
            }
            return [];
        }

        /// <summary>
        /// Gets the starting and ending column numbers from a list of used cap table columns.
        /// </summary>
        /// <param name="usedCapTableColumns">The list of used cap table columns.</param>
        /// <param name="startingCapColumn">The output parameter that will contain the starting column number.</param>
        /// <param name="endCapColumn">The output parameter that will contain the ending column number.</param>
        public static void GetStartAndEndColumn(List<int> usedCapTableColumns, out int startingCapColumn, out int endCapColumn)
        {
            startingCapColumn = usedCapTableColumns.Count > 0 ? usedCapTableColumns.First() : 0;
            endCapColumn = usedCapTableColumns.Count > 0 ? usedCapTableColumns.Last() : 0;
        }
        /// <summary>
        /// Retrieves a list of KpiFieldValueModel objects from the specified worksheet, used rows, excel code statuses, and used cap Kpi column.
        /// </summary>
        /// <param name="worksheet">The worksheet containing the data.</param>
        /// <param name="usedRow">The list of used rows.</param>
        /// <param name="excelCodeStatuses">The list of excel code statuses.</param>
        /// <param name="usedCapKpiColumn">The column number of the used cap Kpi.</param>
        /// <returns>A list of KpiFieldValueModel objects.</returns>
        public static List<KpiFieldValueModel> GetCapTableFieldValueList(IXLWorksheet worksheet, List<IXLRow> usedRow, List<ExcelCodeStatus> excelCodeStatuses, int usedCapKpiColumn)
        {
            return usedRow.SelectMany(x => x.CellsUsed().Where(x => x.Address.ColumnNumber > 2 && x.Address.ColumnNumber > usedCapKpiColumn).Select(x => new KpiFieldValueModel()
            {
                Address = x.Address.ToString(),
                RowNumber = x.Address.RowNumber,
                ColumnNumber = x.Address.ColumnNumber,
                KpiValue = BulkUploadHelper.FormattingValue(x, excelCodeStatuses),
                DataType = x.DataType,
                KpiTextValue = x.GetRichText()?.Text,
                KpiId = GetCellValue(worksheet,x.Address.RowNumber,2,excelCodeStatuses,x.Address.ToString()), // Get kpiId from column number 2
                CellFormat = x.GetFormattedString().Contains(Constants.KpiInfoPercent) ? Constants.KpiInfoPercent : x.Style.NumberFormat?.ToString()
            })).ToList();
        }
        /// <summary>
        /// Retrieves a list of <see cref="KpiFieldValueModel"/> objects representing the column field values in a given range of columns.
        /// </summary>
        /// <param name="worksheet">The Excel worksheet containing the data.</param>
        /// <param name="usedRow">The list of rows in the worksheet that are being used.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="startingColumn">The starting column number.</param>
        /// <param name="endColumn">The ending column number.</param>
        /// <returns>A list of <see cref="KpiFieldValueModel"/> objects representing the column field values.</returns>
        public static List<KpiFieldValueModel> GetCapTableColumnFieldValueList(IXLWorksheet worksheet, List<IXLRow> usedRow, List<ExcelCodeStatus> excelCodeStatuses, int startingColumn, int endColumn)
        {
            return usedRow.SelectMany(x => x.CellsUsed().Where(x => x.Address.ColumnNumber > 2 && x.Address.ColumnNumber >= startingColumn && x.Address.ColumnNumber <= endColumn).Select(x => new KpiFieldValueModel()
            {
                Address = x.Address.ToString(),
                RowNumber = x.Address.RowNumber,
                ColumnNumber = x.Address.ColumnNumber,
                KpiValue = BulkUploadHelper.FormattingValue(x, excelCodeStatuses),
                DataType = x.DataType,
                KpiTextValue = x.GetRichText()?.Text,
                KpiId = GetCellValue(worksheet, x.Address.RowNumber, 2, excelCodeStatuses,x.Address.ToString()),
                CellFormat = x.GetFormattedString().Contains(Constants.KpiInfoPercent) ? Constants.KpiInfoPercent : x.Style.NumberFormat?.ToString(),
                ValueTypeId = (int)ReportValueTypes.Actual,
                ColumnKpiId = GetCellValue(worksheet, 3, x.Address.ColumnNumber, excelCodeStatuses,x.Address.ToString())
            })).ToList();
        }
        /// <summary>
        /// Retrieves the integer value of a cell in the specified worksheet at the given row and column.
        /// </summary>
        /// <param name="worksheet">The worksheet to retrieve the cell value from.</param>
        /// <param name="rowNo">The row number of the cell.</param>
        /// <param name="cellNo">The column number of the cell.</param>
        /// <returns>The integer value of the cell.</returns>
        private static int GetCellValue(IXLWorksheet worksheet, int rowNo, int cellNo, List<ExcelCodeStatus> excelCodeStatuses,string address)
        {
            _ = int.TryParse(worksheet.Cell(rowNo, cellNo).GetValue<string>(), out int cellValue);
            if (cellValue == 0)
            {
                BulkUploadHelper.CreateCellError(address, excelCodeStatuses, MessageConstants.RowOrColumnEmpty);
            }
            return cellValue;
        }

        /// <summary>
        /// Processes the Excel cell values for bulk upload data.
        /// </summary>
        /// <param name="bulkUploadDataModel">The bulk upload data model.</param>
        /// <param name="statuses">The list of statuses.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="pcKpiValueModels">The list of PC KPI value models.</param>
        /// <param name="pcCapTableUploadModels">The list of PC CapTable upload models.</param>
        /// <param name="workSheet">The Excel worksheet.</param>
        /// <param name="moduleId">The module ID.</param>
        /// <param name="kpiModels">The list of FoF KPI models.</param>
        /// <param name="usedCapTableColumns">The list of used CapTable columns.</param>
        /// <param name="bulkUploadHeaders">The list of bulk upload header models.</param>
        /// <param name="bulkUploadPeriodHeader">The list of bulk upload period header models.</param>
        public static void ProcessExcelCellValues(BulkUploadDataModel bulkUploadDataModel, List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, List<PcKpiValueModel> pcKpiValueModels, List<PcCapTableUploadModel> pcCapTableUploadModels, IXLWorksheet workSheet, int moduleId, List<FoFKpiModel> kpiModels, List<int> usedCapTableColumns, List<BulkUploadHeaderModel> bulkUploadHeaders, List<BulkUploadHeaderModel> bulkUploadPeriodHeader)
        {
            var lastRow = workSheet.LastRowUsed().RowNumber();
            var rowUsed = workSheet.RowsUsed().Where(x => x.RowNumber() > 5 && x.RowNumber() <= lastRow).ToList();
            ///get period value for row kpi
            var rowKpiValues = ProcessCapTableRows(bulkUploadDataModel, excelCodeStatuses, pcKpiValueModels, workSheet, moduleId, kpiModels, bulkUploadHeaders, rowUsed, usedCapTableColumns);
            AddKpiValues(pcKpiValueModels, rowKpiValues);
            var columnKpiValues = ProcessCapTableColumns(bulkUploadPeriodHeader, bulkUploadDataModel, excelCodeStatuses, workSheet, moduleId, kpiModels, rowUsed, usedCapTableColumns);
            AddKpiValues(pcKpiValueModels, columnKpiValues);
            AddCapTablePeriod(excelCodeStatuses, pcKpiValueModels, pcCapTableUploadModels, workSheet, moduleId);
            SetErrors(statuses, excelCodeStatuses, workSheet);
        }

        /// <summary>
        /// Sets common validation for the cap table.
        /// </summary>
        /// <param name="statuses">The list of statuses.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="workSheet">The Excel worksheet.</param>
        /// <param name="subSectionFields">The list of sub-section fields.</param>
        /// <param name="kpiModels">The list of KPI models.</param>
        /// <param name="usedCapTableColumns">The list of used cap table columns.</param>
        /// <param name="bulkUploadHeaders">The list of bulk upload headers.</param>
        /// <param name="bulkUploadPeriodHeader">The list of bulk upload period headers.</param>
        public static void SetCommonValidation(List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet workSheet, List<MSubSectionFields> subSectionFields, List<FoFKpiModel> kpiModels, List<int> usedCapTableColumns, List<BulkUploadHeaderModel> bulkUploadHeaders, List<BulkUploadHeaderModel> bulkUploadPeriodHeader, int moduleId =0)
        {
            BulkUploadHelper.SetCapTableHeaderValidation(excelCodeStatuses, workSheet, bulkUploadHeaders, subSectionFields,false);
            BulkUploadHelper.SetCapTableHeaderValidation(excelCodeStatuses, workSheet, bulkUploadPeriodHeader, subSectionFields,true, moduleId);
            BulkUploadHelper.MandatoryColumnValidation(excelCodeStatuses, workSheet, kpiModels.Where(x => x.KpiTypeId == 1).ToList(), 5, true);
            CapTableHelper.GetStartAndEndColumn(usedCapTableColumns, out int startingCapColumn, out int endCapColumn);
            if (startingCapColumn > 0 && endCapColumn > 0)
            {
                BulkUploadHelper.MandatoryCapTableKpiColumnValidation(excelCodeStatuses, workSheet, kpiModels.Where(x => x.KpiTypeId == 2).ToList(), startingCapColumn, endCapColumn);
            }
            BulkUploadHelper.UnmatchedCapTableColumnValidation(excelCodeStatuses, workSheet);
        }
    }
}