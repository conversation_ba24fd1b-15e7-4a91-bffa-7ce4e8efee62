using System;
using System.Threading.Tasks;
using API.Filters;
using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using Contract.Utility;
using ManagedAccounts.Models.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace API.Controllers.ManagedAccounts
{
    /// <summary>
    /// Controller for managed account cap table operations
    /// </summary>
    [Route("api")]
    [ApiController]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    public class ManagedCapTableController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<ManagedCapTableController> _logger;

        public ManagedCapTableController(
            IMediator mediator,
            ILogger<ManagedCapTableController> logger)
        {
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets the managed cap table configuration for a managed account
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="moduleName">The module name for filtering configuration</param>
        /// <returns>The cap table configuration including periods</returns>
        [HttpGet("managedcap-table/config")]
        [AuthorizeUserPermission(Features.ManagedAccounts, Actions.canView)]
        public async Task<IActionResult> GetManagedCapTableConfig(
            [FromQuery] Guid managedAccountId,
            [FromQuery] string moduleName)
        {
            try
            {
                _logger.LogInformation("Getting managed cap table configuration for account: {ManagedAccountId}, module: {ModuleName}", 
                    managedAccountId, moduleName);

                if (managedAccountId == Guid.Empty)
                {
                    _logger.LogWarning("Invalid managed account ID provided: {ManagedAccountId}", managedAccountId);
                    return BadRequest(new { Error = "ManagedAccountId cannot be empty" });
                }

                if (string.IsNullOrWhiteSpace(moduleName))
                {
                    _logger.LogWarning("Invalid module name provided: {ModuleName}", moduleName);
                    return BadRequest(new { Error = "ModuleName cannot be null or empty" });
                }

                var query = new GetManagedCapTableConfigQuery
                {
                    ManagedAccountId = managedAccountId,
                    ModuleName = moduleName
                };

                var result = await _mediator.Send(query);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Successfully retrieved managed cap table configuration for account: {ManagedAccountId}", 
                        managedAccountId);
                    return Ok(result.ConfigData);
                }

                _logger.LogWarning("Failed to get managed cap table configuration: {ErrorMessage}", result.ErrorMessage);
                return NotFound(new { Error = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting managed cap table configuration for account: {ManagedAccountId}, module: {ModuleName}", 
                    managedAccountId, moduleName);
                return StatusCode(500, new { Error = "An unexpected error occurred while retrieving the cap table configuration" });
            }
        }

        /// <summary>
        /// Gets the managed cap table values for a managed account
        /// </summary>
        /// <param name="query">The cap table values query containing all required parameters</param>
        /// <returns>The cap table values response</returns>
        [HttpPost("managedcap-table/values")]
        [AuthorizeUserPermission(Features.ManagedAccounts, Actions.canView)]
        public async Task<IActionResult> GetManagedCapTableValues([FromBody] GetManagedCapTableValuesQuery query)
        {
            try
            {
                _logger.LogInformation("Getting managed cap table values for account: {ManagedAccountId}, period: {PeriodId}, module: {ModuleName}", 
                    query.ManagedAccountId, query.PeriodId, query.ModuleName);

                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for managed cap table values query");
                    return BadRequest(ModelState);
                }

                var result = await _mediator.Send(query);

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Successfully retrieved managed cap table values for account: {ManagedAccountId}", 
                        query.ManagedAccountId);
                    return Ok(result.CapTableData);
                }

                _logger.LogWarning("Failed to get managed cap table values: {ErrorMessage}", result.ErrorMessage);
                return NotFound(new { Error = result.ErrorMessage });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting managed cap table values for account: {ManagedAccountId}, period: {PeriodId}, module: {ModuleName}", 
                    query?.ManagedAccountId, query?.PeriodId, query?.ModuleName);
                return StatusCode(500, new { Error = "An unexpected error occurred while retrieving the cap table values" });
            }
        }
    }
}
