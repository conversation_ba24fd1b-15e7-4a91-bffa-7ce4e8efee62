using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using ClosedXML.Excel;
using Contract.BulkUpload;
using Contract.Common;
using Contract.KPI;
using Contract.PortfolioCompany;
using Contract.Utility;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.PageSettings;
using Imports.Models;
using Shared;
using Utility.Helpers;
namespace Imports.Helpers
{
    public static partial class BulkUploadHelper
    {
        /// <summary>
        /// Sets the header validation for bulk upload.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="worksheet">The Excel worksheet.</param>
        /// <param name="bulkUploadHeaders">The list of bulk upload headers.</param>
        public static void SetHeaderValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<BulkUploadHeaderModel> bulkUploadHeaders, List<MSubSectionFields> subSectionFields, int? moduleId = 0)
        {
            bulkUploadHeaders.ForEach(x =>
            {
                CheckDuplicateHeader(excelCodeStatuses, bulkUploadHeaders, x);
                string headerValue = null;
                Match match = Regex.Match(x.HeaderValue, @"\(([^)]*)\)", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
                if (match.Success && match.Groups[1]?.Value != null)
                {
                    headerValue = match.Groups[1].Value;
                }
                ExcelHeaderValidation(excelCodeStatuses, subSectionFields, x, headerValue, moduleId);
                if (GetAliasNameValidateModuleTypes().Contains(moduleId ?? 0))
                    PageConfigHeaderValidatePeriodValidation(x, excelCodeStatuses);
                else
                    SetPeriodValidation(x, excelCodeStatuses);
            });
            if (string.IsNullOrEmpty(worksheet.Cell(1, 1).GetValue<string>()?.Trim()) || (worksheet.Cell(1, 1).GetValue<string>()?.Trim() != Constants.KpiTitle && worksheet.Cell(1, 1).GetValue<string>()?.Trim() != Constants.LineItemTitle))
            {
                CreateCellError(Constants.FirstColumn, excelCodeStatuses, MessageConstants.KpiColumnEmpty);
            }
            if (string.IsNullOrEmpty(worksheet.Cell(1, 2).GetValue<string>()?.Trim()) || worksheet.Cell(1, 2).GetValue<string>()?.Trim() != Constants.ExcelKpiId)
            {
                CreateCellError(Constants.SecondColumn, excelCodeStatuses, MessageConstants.IdEmpty);
            }
        }

        /// <summary>
        /// SetMonthlyReportHeaderValidation
        /// </summary>
        /// <param name="excelCodeStatuses"></param>
        /// <param name="worksheet"></param>
        /// <param name="bulkUploadHeaders"></param>
        /// <param name="subSectionFields"></param>
        /// <param name="moduleId"></param>
        public static void SetMonthlyReportHeaderValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<BulkUploadHeaderModel> bulkUploadHeaders, List<MSubSectionFields> subSectionFields, int? moduleId = 0)
        {
            bulkUploadHeaders.ForEach(x =>
            {
                CheckDuplicateHeader(excelCodeStatuses, bulkUploadHeaders, x);
            });
            if (string.IsNullOrEmpty(worksheet.Cell(1, 1).GetValue<string>()?.Trim()) || worksheet.Cell(1, 1).GetValue<string>()?.Trim() != Constants.Measure)
            {
                CreateCellError(Constants.FirstColumn, excelCodeStatuses, MessageConstants.MeasureColumnEmpty);
            }
        }

        private static void CheckDuplicateHeader(List<ExcelCodeStatus> excelCodeStatuses, List<BulkUploadHeaderModel> bulkUploadHeaders, BulkUploadHeaderModel x)
        {
            if (bulkUploadHeaders.Count(h => h.HeaderValue.Equals(x.HeaderValue, System.StringComparison.CurrentCultureIgnoreCase)) > 1)
            {
                CreateCellError(x.Address, excelCodeStatuses, MessageConstants.DuplicateHeaders);
            }
        }

        private static void ExcelHeaderValidation(List<ExcelCodeStatus> excelCodeStatuses, List<MSubSectionFields> subSectionFields, BulkUploadHeaderModel x, string headerValue, int? moduleId = 0)
        {
            if (headerValue != null && x.ValueTypeId > 0 && moduleId < (int)KpiModuleType.ManagedCaptable1) // ignored for ManagedCaptable
            {
                var periods = GetAliasNameValidateModuleTypes().Contains(moduleId ?? 0) ? subSectionFields.FirstOrDefault(y => y.AliasName == headerValue) : subSectionFields.FirstOrDefault(y => y.Name == headerValue);
                if (periods != null && periods.ChartValue != null)
                {
                    switch (true)
                    {
                        case var _ when x.Month > 0 && !periods.ChartValue.Contains(Constants.AdhocMonthly):
                            CreateCellError(x.Address, excelCodeStatuses, MessageConstants.MonthPeriodInvalid);
                            break;
                        case var _ when !string.IsNullOrEmpty(x.Quarter) && !periods.ChartValue.Contains(Constants.AdhocQuarterly):
                            CreateCellError(x.Address, excelCodeStatuses, MessageConstants.QuarterlyPeriodInvalid);
                            break;
                        case var _ when string.IsNullOrEmpty(x.Quarter) && (x.Month == null || x.Month == 0) && x.Year > 0 && !periods.ChartValue.Contains(Constants.Annual):
                            CreateCellError(x.Address, excelCodeStatuses, MessageConstants.AnnualPeriodInvalid);
                            break;
                        case var _ when !string.IsNullOrEmpty(x.Half) && string.IsNullOrEmpty(x.Quarter) && (x.Month == null || x.Month == 0) && x.Year > 0 && !periods.ChartValue.Contains(Constants.HalfAnnual):
                            CreateCellError(x.Address, excelCodeStatuses, MessageConstants.HalfAnnualPeriodInvalid);
                            break;
                    }
                }
                else
                {
                    CreateCellError(x.Address, excelCodeStatuses, MessageConstants.PeriodNotMapped);
                }
            }
        }

        public static void SetCapTableHeaderValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<BulkUploadHeaderModel> bulkUploadHeaders, List<MSubSectionFields> subSectionFields, bool isPeriodHeader = false, int moduleId = 0)
        {
            bulkUploadHeaders.ForEach(x =>
            {
                CheckDuplicateHeader(excelCodeStatuses, bulkUploadHeaders, x);
                if (x.HeaderValue != null && x.ValueTypeId > 0 && isPeriodHeader)
                {
                    ExcelHeaderValidation(excelCodeStatuses, subSectionFields, x, "Period Type", moduleId);
                }
                SetCapTablePeriodValidation(x, excelCodeStatuses);
            });
            if (string.IsNullOrEmpty(worksheet.Cell(5, 1).GetValue<string>()?.Trim()) || worksheet.Cell(5, 1).GetValue<string>()?.Trim() != Constants.InstrumentTitle)
            {
                CreateCellError(Constants.FirstColumn, excelCodeStatuses, MessageConstants.InstrumentColumnEmpty);
            }
        }
        /// <summary>
        /// Performs mandatory column validation for the given Excel worksheet and KPI models.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="worksheet">The Excel worksheet to validate.</param>
        /// <param name="kpiModels">The list of KPI models.</param>
        public static void MandatoryColumnValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<FoFKpiModel> kpiModels, int rowNumber = 1, bool isCapTable = false)
        {
            var usedCellCountInFirstColumn = worksheet.Column(1).CellsUsed().Count() - (isCapTable ? 3 : 1);
            if (usedCellCountInFirstColumn != kpiModels.Count)
            {
                if (isCapTable)
                    CreateCellError(string.Empty, excelCodeStatuses, MessageConstants.InstrumentColumnEmpty);
                else
                    CreateCellError(string.Empty, excelCodeStatuses, MessageConstants.KpiValidation);
            }
            worksheet.Column(1).CellsUsed().Where(x => x.Address.RowNumber > rowNumber).ToList().ForEach(x =>
            {
                if (kpiModels.Any(y => y.KPI == x.GetValue<string>()) == false)
                {
                    if (isCapTable)
                        CreateCellError(x.Address.ToString(), excelCodeStatuses, MessageConstants.InstrumentColumnEmpty);
                    else
                        CreateCellError(x.Address.ToString(), excelCodeStatuses, MessageConstants.KpiValidation);
                }
            });
            worksheet.Column(2).CellsUsed().Where(x => x.Address.RowNumber > rowNumber).ToList().ForEach(x =>
            {
                _ = int.TryParse(x.GetValue<string>(), out int kpiId);
                if (kpiModels.Any(y => y.KpiId == kpiId) == false)
                {
                    CreateCellError(x.Address.ToString(), excelCodeStatuses, MessageConstants.KpiIdEmpty);
                }
            });
            if (isCapTable && !kpiModels.Any(x => x.KpiTypeId == 1))
            {
                CreateCellError(string.Empty, excelCodeStatuses, MessageConstants.RowKpiEmpty);
            }
        }
        /// <summary>
        /// MonthlyReportMandatoryColumnValidation
        /// </summary>
        /// <param name="excelCodeStatuses"></param>
        /// <param name="worksheet"></param>
        /// <param name="kpiModels"></param>
        /// <param name="bulkUploadHeaders"></param>
        public static void MonthlyReportMandatoryColumnValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<FoFKpiModel> kpiModels, List<BulkUploadHeaderModel> bulkUploadHeaders)
        {
            var usedCellCountInFirstColumn = worksheet.Column(1).CellsUsed().Count() - 1;
            if (usedCellCountInFirstColumn != kpiModels.Count)
            {
                CreateCellError(string.Empty, excelCodeStatuses, MessageConstants.MeasureColumnEmpty);
            }
            worksheet.Column(1).CellsUsed().Where(x => x.Address.RowNumber > 1).ToList().ForEach(x =>
            {
                if (kpiModels.Any(y => y.KPI == x.GetValue<string>()) == false)
                {
                    CreateCellError(x.Address.ToString(), excelCodeStatuses, MessageConstants.KpiColumnEmpty);
                }
            });
            if (!Constants.MonthlyReportStaticColumns.All(column => (bool)(bulkUploadHeaders?.Select(header => header.HeaderValue)?.ToList().Contains(column))))
            {
                CreateCellError(string.Empty, excelCodeStatuses, MessageConstants.MandatoryColumnsMissing);
            }
        }
        public static void MandatoryCapTableKpiColumnValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<FoFKpiModel> kpiModels, int startColumn, int endColumn)
        {
            var usedCellCountInFirstColumn = worksheet.Row(3).CellsUsed().Count();
            if (usedCellCountInFirstColumn != kpiModels.Where(x => x.KpiTypeId == 2).ToList().Count)
            {
                CreateCellError(Constants.FirstColumn, excelCodeStatuses, MessageConstants.KpiEmpty);
            }
            worksheet.Row(5).CellsUsed().Where(x => x.Address.ColumnNumber >= startColumn && x.Address.ColumnNumber <= endColumn).ToList().ForEach(x =>
            {
                if (kpiModels.Any(y => y.KPI == x.GetValue<string>()) == false)
                {
                    CreateCellError(x.Address.ToString(), excelCodeStatuses, MessageConstants.KpiEmpty);
                }
            });
            worksheet.Row(3).CellsUsed().Where(x => x.Address.ColumnNumber >= startColumn).ToList().ForEach(x =>
            {
                _ = int.TryParse(x.GetValue<string>(), out int kpiId);
                if (kpiModels.Any(y => y.KpiId == kpiId) == false)
                {
                    CreateCellError(x.Address.ToString(), excelCodeStatuses, MessageConstants.KpiIdEmpty);
                }
            });
        }
        /// <summary>
        /// Validates unmatched columns in the Excel worksheet based on the provided bulk upload headers.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="worksheet">The Excel worksheet.</param>
        /// <param name="bulkUploadHeaders">The list of bulk upload headers.</param>
        public static void UnmatchedColumnValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<BulkUploadHeaderModel> bulkUploadHeaders)
        {
            List<int> usedColumns = worksheet.ColumnsUsed().Select(x => x.ColumnNumber()).ToList();
            List<int> unmatchedColumns = usedColumns?.Except(bulkUploadHeaders?.Select(x => x.ColumnNumber))?.ToList();
            UnMatchedValidation(excelCodeStatuses, worksheet, unmatchedColumns);
        }

        private static void UnMatchedValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<int> unmatchedColumns)
        {
            if (unmatchedColumns?.Any(x => x > 3) == true)
            {
                unmatchedColumns.Where(x => x > 3).ToList().ForEach(x =>
                {
                    CreateCellError(worksheet.Column(x).ColumnLetter(), excelCodeStatuses, MessageConstants.PeriodEmpty);
                });
            }
        }

        public static void UnmatchedCapTableColumnValidation(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet)
        {
            List<int> usedColumns = worksheet.ColumnsUsed().Select(x => x.ColumnNumber()).ToList();
            List<int> unmatchedColumns = usedColumns?.Except(worksheet.Row(5).CellsUsed().Where(x => x.Address.ColumnNumber > 2).ToList()?.Select(x => x.Address.ColumnNumber))?.ToList();
            UnMatchedValidation(excelCodeStatuses, worksheet, unmatchedColumns);
        }
        /// <summary>
        /// Adds a file message to the list of statuses.
        /// </summary>
        /// <param name="statuses">The list of statuses.</param>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="sheetName">The name of the sheet.</param>
        /// <param name="code">The code for the status.</param>
        /// <param name="message">The message for the status.</param>
        public static void AddFileMessage(List<Status> statuses, List<ExcelCodeStatus> excelCodeStatuses, string sheetName, string code, string message)
        {
            statuses.Add(new Status()
            {
                Code = code,
                Message = message,
                excelStatuses = [new ExcelStatus() { SheetName = sheetName, ExcelCodeStatus = excelCodeStatuses, FileName = string.Empty }]
            });
        }
        /// <summary>
        /// Retrieves the headers from the specified worksheet and returns a list of BulkUploadHeaderModel objects.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of ExcelCodeStatus objects.</param>
        /// <param name="worksheet">The IXLWorksheet object representing the worksheet.</param>
        /// <param name="valueTypes">The list of M_ValueTypes objects.</param>
        /// <param name="subSectionFields">The list of MSubSectionFields objects.</param>
        /// <returns>A list of BulkUploadHeaderModel objects representing the headers.</returns>
        public static List<BulkUploadHeaderModel> GetHeaders(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, List<M_ValueTypes> valueTypes, List<MSubSectionFields> subSectionFields, int? moduleId)
        {
            var colNo = 3;
            if (moduleId == (int)KpiModuleType.MonthlyReport)
            {
                return MapMonthlyReportData(excelCodeStatuses, worksheet);
            }
            else
            {
                return worksheet.Row(1).CellsUsed().Where(x => x.Address.ColumnNumber > colNo).Select(x => new BulkUploadHeaderModel()
                {
                    Address = x.Address.ToString(),
                    RowNumber = x.Address.RowNumber,
                    ColumnNumber = x.Address.ColumnNumber,
                    HeaderValue = x.GetValue<string>()?.Trim(),
                    ValueTypeId = moduleId != (int)KpiModuleType.MonthlyReport ? GetValueTypeId(x.Address.ToString(), x.GetValue<string>()?.Trim(), valueTypes, subSectionFields, excelCodeStatuses, moduleId) : 0,
                    Year = GetYear(excelCodeStatuses, x.GetValue<string>()?.Trim(), x.Address.ToString()),
                    Quarter = GetQuarter(x.GetValue<string>()?.Trim()),
                    Half = GetHalf(x.GetValue<string>()?.Trim()),
                    Month = GetMonth(x.GetValue<string>()?.Trim()),
                }).ToList();
            }
        }
        /// <summary>
        /// MapMonthlyReportData
        /// </summary>
        /// <param name="excelCodeStatuses"></param>
        /// <param name="worksheet"></param>
        /// <returns></returns>
        private static List<BulkUploadHeaderModel> MapMonthlyReportData(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet)
        {
            return worksheet.Row(1).CellsUsed().Where(x => x.Address.ColumnNumber > 1 && x.Address.ColumnNumber > 1).Select(x => new BulkUploadHeaderModel()
            {
                Address = x.Address.ToString(),
                RowNumber = x.Address.RowNumber,
                ColumnNumber = x.Address.ColumnNumber,
                HeaderValue = x.GetValue<string>()?.Trim(),
                ValueTypeId = x.GetValue<string>()?.Trim().Split(",").FirstOrDefault() switch
                {
                    Constants.BudYTD => (int)ReportValueTypes.BudgetYtd,
                    Constants.CalculationYTD => (int)ReportValueTypes.ActualYTD,
                    Constants.BudVar => (int)ReportValueTypes.BudgetVar,
                    Constants.PriorYtd => (int)ReportValueTypes.PriorYtd,
                    Constants.PriorVar => (int)ReportValueTypes.PriorVar,
                    _ => (int)ReportValueTypes.Actual,
                },
                Year = !Constants.MonthlyReportStaticColumns.Contains(x.GetValue<string>()?.Trim()) ? GetYear(excelCodeStatuses, x.GetValue<string>()?.Trim(), x.Address.ToString(), true) : 0,
                Month = !Constants.MonthlyReportStaticColumns.Contains(x.GetValue<string>()?.Trim()) ? GetMonth(x.GetValue<string>()?.Trim()) : 0,
            }).ToList();
        }

        public static List<BulkUploadHeaderModel> GetCapTableHeaders(List<ExcelCodeStatus> excelCodeStatuses, IXLWorksheet worksheet, int columnNo)
        {
            return worksheet.Row(5).CellsUsed().Where(x => x.Address.ColumnNumber > 2 && x.Address.ColumnNumber > columnNo).Select(x => new BulkUploadHeaderModel()
            {
                Address = x.Address.ToString(),
                RowNumber = x.Address.RowNumber,
                ColumnNumber = x.Address.ColumnNumber,
                HeaderValue = x.GetValue<string>()?.Trim(),
                ValueTypeId = x.GetValue<string>()?.Trim().Split().FirstOrDefault() switch
                {
                    Constants.CalculationLTM => (int)ReportValueTypes.ActualLTM,
                    Constants.CalculationYTD => (int)ReportValueTypes.ActualYTD,
                    _ => (int)ReportValueTypes.Actual,
                },
                Year = GetYear(excelCodeStatuses, x.GetValue<string>()?.Trim(), x.Address.ToString()),
                Quarter = GetQuarter(x.GetValue<string>()?.Trim()),
                Month = GetMonth(x.GetValue<string>()?.Trim()),
                KpiId = worksheet.Cell(3, x.Address.ColumnNumber).TryGetValue<int>(out int kpiId) ? kpiId : 0
            }).ToList();
        }
        /// <summary>
        /// Adds a new status to the list of statuses and returns the updated list.
        /// </summary>
        /// <param name="statuses">The list of statuses.</param>
        /// <param name="code">The code of the new status.</param>
        /// <param name="message">The message of the new status.</param>
        /// <returns>The updated list of statuses.</returns>
        public static List<Status> SetSheetValidation(List<Status> statuses, string code, string message)
        {
            statuses.Add(new Status()
            {
                Code = code,
                Message = message
            });
            return statuses;
        }

        /// <summary>
        /// Sets the financial sheet validation status and adds it to the list of statuses.
        /// </summary>
        /// <param name="statuses">The list of statuses to add the financial sheet validation status to.</param>
        /// <param name="code">The code of the status.</param>
        /// <param name="message">The message of the status.</param>
        /// <param name="sheetName">The name of the financial sheet.</param>
        /// <returns>The updated list of statuses.</returns>
        public static List<Status> SetFinancialSheetValidation(List<Status> statuses, string code, string message, string sheetName)
        {
            var status = CreateStatus(code, message, sheetName);
            statuses.Add(status);
            return statuses;
        }

        /// <summary>
        /// Represents the status of an operation.
        /// </summary>
        private static Status CreateStatus(string code, string message, string sheetName)
        {
            return new Status
            {
                Code = code,
                Message = message,
                excelStatuses = [CreateExcelStatus(sheetName, message, code)]
            };
        }

        /// <summary>
        /// Represents the status of an Excel sheet.
        /// </summary>
        private static ExcelStatus CreateExcelStatus(string sheetName, string message, string code)
        {
            return new ExcelStatus
            {
                SheetName = sheetName,
                ExcelCodeStatus =
        [
            new ExcelCodeStatus
            {
                Code = code,
                Message = message,
                CellCode = string.Empty
            }
        ]
            };
        }
        /// <summary>
        /// Sets the period validation for a given BulkUploadHeaderModel.
        /// </summary>
        /// <param name="x">The BulkUploadHeaderModel object.</param>
        /// <param name="excelCodeStatuses">The list of ExcelCodeStatus objects.</param>
        public static void SetPeriodValidation(BulkUploadHeaderModel x, List<ExcelCodeStatus> excelCodeStatuses)
        {
            string cellValue = x.HeaderValue?.Trim();
            if (string.IsNullOrWhiteSpace(cellValue) || cellValue.Split(" ").Length > 3 || cellValue.Split("-").Length > 2)
            {
                CreateCellError(x.Address, excelCodeStatuses, MessageConstants.InvalidHeader);
            }
            else if (cellValue.Split(" ").Length == 3 && x.ValueTypeId < 10)
            {
                CreateCellError(x.Address, excelCodeStatuses, MessageConstants.InvalidHeader);
            }
            else if (cellValue.Split(" ").Length == 3 && cellValue.Split("-").Length == 2)
            {
                string period = cellValue.Split(" ").LastOrDefault()?.Split("-")[0].Trim().ToLower();
                SetMonthAndYearValidation(x, excelCodeStatuses, period);
            }
            else if (cellValue.Split(" ").Length == 2 && cellValue.Split("-").Length == 2)
            {
                string period = cellValue?.Split(" ").LastOrDefault()?.Split("-")[0].Trim().ToLower();
                SetMonthAndYearValidation(x, excelCodeStatuses, period);
            }
        }
        public static void PageConfigHeaderValidatePeriodValidation(BulkUploadHeaderModel x, List<ExcelCodeStatus> excelCodeStatuses)
        {
            string cellValue = x.HeaderValue?.Trim();
            string pattern = @"^\(.*\)\s*(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|Q[1-4])?-?\d{4}$";
            if (string.IsNullOrWhiteSpace(cellValue) || !Regex.IsMatch(cellValue, pattern, RegexOptions.None, TimeSpan.FromMilliseconds(250)) || x.ValueTypeId == 0)
            {
                CreateCellError(x.Address, excelCodeStatuses, MessageConstants.InvalidHeader);
            }
            else
            {
                var match = Regex.Match(cellValue, pattern, RegexOptions.None, TimeSpan.FromMilliseconds(250));
                if (match.Success)
                {
                    string period = match.Value.Split(' ').LastOrDefault()?.Trim();
                    if (period.Contains("-"))
                    {
                        string lastPartPeriod = period.Split('-').First().ToLower();
                        SetMonthAndYearValidation(x, excelCodeStatuses, lastPartPeriod);
                    }
                }
            }
        }
        private static void SetMonthAndYearValidation(BulkUploadHeaderModel x, List<ExcelCodeStatus> excelCodeStatuses, string period)
        {
            if (Common.GetQuarterNumber(period?.ToUpper()) == 0 && Common.GetMonthNumber(period) == 0 && Common.GetHalfNumber(period?.ToUpper()) == 0)
            {
                CreateCellError(x.Address, excelCodeStatuses, MessageConstants.PeriodNotFound);
            }
        }

        public static void SetCapTablePeriodValidation(BulkUploadHeaderModel x, List<ExcelCodeStatus> excelCodeStatuses)
        {
            string cellValue = x.HeaderValue?.Trim();
            if (string.IsNullOrWhiteSpace(cellValue) || cellValue.Split(" ").Length > 2 || cellValue.Split("-").Length > 2)
            {
                CreateCellError(x.Address, excelCodeStatuses, MessageConstants.InvalidHeader);
            }
            else if (cellValue.Split(" ").Length == 2 && x.ValueTypeId < 10)
            {
                CreateCellError(x.Address, excelCodeStatuses, MessageConstants.InvalidHeader);
            }
            else if (cellValue.Split(" ").Length == 2 && cellValue.Split("-").Length == 2)
            {
                string period = cellValue.Split(" ").LastOrDefault()?.Split("-")[0].Trim().ToLower();
                SetMonthAndYearValidation(x, excelCodeStatuses, period);
            }
            else if (cellValue.Split("-").Length == 2)
            {
                string period = cellValue.Split("-")[0].Trim().ToLower();
                SetMonthAndYearValidation(x, excelCodeStatuses, period);
            }
        }
        /// <summary>
        /// Creates a table name by appending the user ID to the given table name.
        /// </summary>
        /// <param name="tableName">The base table name.</param>
        /// <param name="userId">The user ID.</param>
        /// <returns>The generated table name.</returns>
        public static string CreateTableName(string tableName, int userId)
        {
            return $"{tableName}{userId}";
        }
        /// <summary>
        /// Creates a cell error and adds it to the error list.
        /// </summary>
        /// <param name="address">The address of the cell.</param>
        /// <param name="errorList">The list of ExcelCodeStatus objects.</param>
        /// <param name="message">The error message.</param>
        public static void CreateCellError(string address, List<ExcelCodeStatus> errorList, string message)
        {
            errorList.Add(new ExcelCodeStatus()
            {
                Code = "error",
                CellCode = address,
                Message = message
            });
        }
        /// <summary>
        /// Gets the formatted value of a KPI (Key Performance Indicator) based on the provided cell format.
        /// </summary>
        /// <param name="kpiValue">The original value of the KPI.</param>
        /// <param name="cellFormat">The format of the cell.</param>
        /// <returns>The formatted value of the KPI.</returns>
        public static string GetFormattedValue(string kpiValue, string cellFormat)
        {
            if (decimal.TryParse(kpiValue?.Replace(Constants.KpiInfoMultiple, string.Empty)?.Replace(Constants.KpiInfoPercent, string.Empty)?.Replace(Constants.KpiInfoCurrency, string.Empty), NumberStyles.Float, CultureInfo.InvariantCulture, out decimal updatedValue))
            {
                if (cellFormat.Contains(Constants.KpiInfoPercent))
                {
                    var result = (updatedValue * 100).ToString();
                    return result.Contains('.') ? result.TrimEnd('0') : result;
                }
                else
                {
                    return updatedValue.ToString();
                }
            }
            return kpiValue;
        }
        /// <summary>
        /// Retrieves the quarter from a given cell value.
        /// </summary>
        /// <param name="cellValue">The cell value to extract the quarter from.</param>
        /// <returns>The quarter extracted from the cell value, or null if the extraction fails.</returns>
        public static string GetQuarter(string cellValue)
        {
            string[] period = cellValue?.Trim()?.Split(" ")?.LastOrDefault()?.Split("-");
            if (period?.Length > 1 && Common.GetQuarterNumber(period[0].ToUpper()) > 0)
            {
                return period[0].ToUpper();
            }
            return null;
        }
        public static string GetHalf(string cellValue)
        {
            string[] period = cellValue?.Trim()?.Split(" ")?.LastOrDefault()?.Split("-");
            if (period?.Length > 1 && Common.GetHalfNumber(period[0].ToUpper()) > 0)
            {
                return period[0].ToUpper();
            }
            return null;
        }
        /// <summary>
        /// Gets the month from a given cell value.
        /// </summary>
        /// <param name="cellValue">The cell value.</param>
        /// <returns>The month number.</returns>
        public static int? GetMonth(string cellValue)
        {
            string[] period = cellValue?.Trim()?.Split(" ")?.LastOrDefault()?.Split("-");
            if (period?.Length > 1 && Common.GetMonthNumber(period[0]?.ToLower()) > 0)
            {
                return Common.GetMonthNumber(period[0]?.ToLower());
            }
            return null;
        }
        /// <summary>
        /// Retrieves the year from a given cell value.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of ExcelCodeStatus objects.</param>
        /// <param name="cellValue">The cell value to extract the year from.</param>
        /// <param name="address">The address of the cell.</param>
        /// <returns>The extracted year as an integer.</returns>
        public static int GetYear(List<ExcelCodeStatus> excelCodeStatuses, string cellValue, string address, bool isMonthly = false)
        {
            string period = cellValue?.Trim()?.Split(" ")?.LastOrDefault();
            if (isMonthly)
            {
                if (!period.Contains('-'))
                {
                    CreateCellError(address, excelCodeStatuses, MessageConstants.InvalidHeader);
                }
            }
            if (period != null && period.Contains('-') && int.TryParse(period.Split("-").LastOrDefault(), out int year) && year >= 2000 && year <= 2100)
            {
                return year;
            }
            else if (int.TryParse(period, out int yearValue) && yearValue >= 2000 && yearValue <= 2100)
            {
                return yearValue;
            }
            else
            {
                CreateCellError(address, excelCodeStatuses, "Please check the year in the header.");
                return 0;
            }
        }
        /// <summary>
        /// Retrieves a list of KpiFieldValueModel objects based on the provided parameters.
        /// </summary>
        /// <param name="kpiModels">The list of FoFKpiModel objects.</param>
        /// <param name="cellValues">The list of KpiFieldValueModel objects representing cell values.</param>
        /// <param name="bulkUploadHeaders">The list of BulkUploadHeaderModel objects representing bulk upload headers.</param>
        /// <param name="companyId">The ID of the company.</param>
        /// <param name="moduleId">The ID of the module.</param>
        /// <returns>A list of KpiFieldValueModel objects.</returns>
        public static List<KpiFieldValueModel> GetKpiDataList(List<FoFKpiModel> kpiModels, List<KpiFieldValueModel> cellValues, List<BulkUploadHeaderModel> bulkUploadHeaders, int companyId, int moduleId)
        {
            if (kpiModels.Count != 0)
            {
                return (from field in cellValues
                        join excelColumn in bulkUploadHeaders on field.ColumnNumber equals excelColumn.ColumnNumber into kc
                        from fieldKpi in kc.DefaultIfEmpty()
                        join sub in kpiModels on field?.KpiId equals sub?.KpiId into kc1
                        from kpi in kc1.DefaultIfEmpty()
                        where kpi?.KpiId != null && fieldKpi?.ValueTypeId != 0 && fieldKpi?.HeaderValue != null && fieldKpi?.ColumnNumber != null
                        select new KpiFieldValueModel()
                        {
                            Quarter = fieldKpi?.Quarter,
                            Half = fieldKpi?.Half,
                            Month = fieldKpi?.Month ?? null,
                            Year = fieldKpi?.Year ?? 0,
                            KpiValue = field?.KpiValue,
                            KpiId = field?.KpiId ?? 0,
                            ModuleId = moduleId,
                            Address = field?.Address,
                            RowNumber = field?.RowNumber ?? 0,
                            ColumnNumber = field?.ColumnNumber ?? 0,
                            KpiInfo = kpi?.KpiInfo,
                            Kpi = kpi?.KPI,
                            DataType = field?.DataType ?? 0,
                            CellFormat = field?.CellFormat,
                            KpiTextValue = field?.KpiTextValue,
                            ValueTypeId = fieldKpi?.ValueTypeId ?? 0,
                            IsMerged = field?.IsMerged ?? false,
                            MergedColRange = field?.MergedColRange,
                        }).ToList();
            }
            return [];
        }
        public static List<KpiFieldValueModel> GetCapTableColumnKpiDataList(List<FoFKpiModel> kpiModels, List<KpiFieldValueModel> cellValues, BulkUploadHeaderModel bulkUploadHeader, int companyId, int moduleId)
        {
            if (kpiModels.Count != 0)
            {
                return (from field in cellValues
                        join sub in kpiModels.Where(x => x.KpiTypeId == 1).ToList() on field?.KpiId equals sub?.KpiId into kc1
                        from kpi in kc1.DefaultIfEmpty()
                        where kpi?.KpiId != null && field?.ValueTypeId != 0 && field?.ColumnNumber != null
                        select new KpiFieldValueModel()
                        {
                            Quarter = bulkUploadHeader?.Quarter,
                            Month = bulkUploadHeader?.Month ?? null,
                            Year = bulkUploadHeader?.Year ?? 0,
                            KpiValue = field?.KpiValue,
                            KpiId = field?.KpiId ?? 0,
                            ModuleId = moduleId,
                            Address = field?.Address,
                            RowNumber = field?.RowNumber ?? 0,
                            ColumnNumber = field?.ColumnNumber ?? 0,
                            KpiInfo = !kpi.IsOverrule ? kpiModels.FirstOrDefault(x => x.KpiId == field.ColumnKpiId)?.KpiInfo : kpi.KpiInfo,
                            Kpi = kpi?.KPI,
                            DataType = field?.DataType ?? 0,
                            CellFormat = field?.CellFormat,
                            KpiTextValue = field?.KpiTextValue,
                            ValueTypeId = bulkUploadHeader?.ValueTypeId ?? 0,
                            ColumnKpiId = field?.ColumnKpiId ?? 0
                        }).ToList();
            }
            return [];
        }
        /// <summary>
        /// Retrieves the value type ID based on the provided address, period, value types, sub section fields, and excel code statuses.
        /// </summary>
        /// <param name="address">The address of the cell.</param>
        /// <param name="period">The period value.</param>
        /// <param name="valueTypes">The list of value types.</param>
        /// <param name="subSectionFields">The list of sub section fields.</param>
        /// <param name="excelCodeStatuses">The list of excel code statuses.</param>
        /// <returns>The value type ID.</returns>
        public static int GetValueTypeId(string address, string period, List<M_ValueTypes> valueTypes, List<MSubSectionFields> subSectionFields, List<ExcelCodeStatus> excelCodeStatuses, int? moduleId = 0)
        {
            if (!string.IsNullOrEmpty(period) && !period.Equals(Constants.KpiTitle, StringComparison.OrdinalIgnoreCase) && !period.Equals(Constants.Measure, StringComparison.OrdinalIgnoreCase) && !period.Equals(Constants.ExcelKpiId, StringComparison.OrdinalIgnoreCase))
            {
                Match match = Regex.Match(period, @"\(([^)]*)\)", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
                if (match.Success && match.Groups[1]?.Value != null)
                {
                    if (GetAliasNameValidateModuleTypes().Contains(moduleId ?? 0))
                    {
                        string aliasPeriodValue = match.Groups[1].Value;
                        if (!string.IsNullOrEmpty(aliasPeriodValue) && !subSectionFields.Any(x => x.AliasName.Equals(aliasPeriodValue, System.StringComparison.CurrentCultureIgnoreCase) && x.ChartValue != null))
                        {
                            CreateCellError(address, excelCodeStatuses, MessageConstants.InValidValueType);
                        }
                        aliasPeriodValue = subSectionFields?.FirstOrDefault(x => x.AliasName.Equals(aliasPeriodValue, System.StringComparison.CurrentCultureIgnoreCase))?.Name ?? string.Empty;
                        return valueTypes?.FirstOrDefault(y => y.HeaderValue.Equals(aliasPeriodValue, System.StringComparison.CurrentCultureIgnoreCase))?.ValueTypeID ?? 0;
                    }
                    else
                    {
                        string periodValue = match.Groups[1].Value;
                        if (!string.IsNullOrEmpty(periodValue) && !subSectionFields.Any(x => x.Name.Equals(periodValue, System.StringComparison.CurrentCultureIgnoreCase) && x.ChartValue != null))
                        {
                            CreateCellError(address, excelCodeStatuses, MessageConstants.InValidValueType);
                        }
                        return valueTypes?.FirstOrDefault(y => y.HeaderValue.Equals(periodValue, System.StringComparison.CurrentCultureIgnoreCase))?.ValueTypeID ?? 0;
                    }
                }
                else
                {
                    CreateCellError(address, excelCodeStatuses, MessageConstants.InValidValueType);
                    return 0;
                }
            }
            return 0;
        }
        /// <summary>
        /// Reads a worksheet from the specified Excel workbook based on the sheet name.
        /// </summary>
        /// <param name="xLWorkbook">The Excel workbook.</param>
        /// <param name="sheetName">The name of the worksheet to read.</param>
        /// <returns>The <see cref="IXLWorksheet"/> object representing the specified worksheet.</returns>
        public static IXLWorksheet ReadFileFromPath(XLWorkbook xLWorkbook, string sheetName)
        {
            return xLWorkbook.Worksheets.FirstOrDefault(x => x.Worksheet.Name == sheetName)?.Worksheet;
        }
        /// <summary>
        /// Processes KPI records by row.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of Excel code statuses.</param>
        /// <param name="kpiFieldValueList">The list of KPI field values.</param>
        /// <returns>The list of PcKpiValueModel objects.</returns>
        public static List<PcKpiValueModel> ProcessKpiRecordsByRow(List<ExcelCodeStatus> excelCodeStatuses, List<KpiFieldValueModel> kpiFieldValueList, bool isMonthlyReport = false, int colNo = 0)
        {
            List<PcKpiValueModel> pcKpiValueModels = [];
            kpiFieldValueList?.GroupBy(x => x.RowNumber)?.ToList()?.ForEach(cells =>
            {
                ProcessKpiByCell(excelCodeStatuses, cells, pcKpiValueModels, isMonthlyReport, colNo);
            });
            return !excelCodeStatuses.Any(x => x.Code == Constants.error) ? pcKpiValueModels : [];
        }
        /// <summary>
        /// Processes the KPI values for a group of cells.
        /// </summary>
        /// <param name="excelCodeStatuses">The list of Excel code status objects.</param>
        /// <param name="cells">The grouping of KPI field value models by cell.</param>
        /// <param name="pcKpiValueModels">The list of PC KPI value models.</param>
        public static void ProcessKpiByCell(List<ExcelCodeStatus> excelCodeStatuses, IGrouping<int, KpiFieldValueModel> cells, List<PcKpiValueModel> pcKpiValueModels, bool isMonthlyReport = false, int colNo = 0)
        {
            foreach (var x in cells)
            {
                if (isMonthlyReport)
                {
                    if (x.ColumnNumber == 1)
                        continue;
                    // Skip blank cells
                    if (string.IsNullOrWhiteSpace(x.KpiValue))
                    {
                        continue;
                    }
                    if (x.ColumnNumber == colNo)
                    {
                        x.KpiInfo = Constants.KpiInfoText;
                        if (!Constants.MonthlyReportColors.Contains(x.KpiValue?.ToLower()))
                        {
                            CreateCellError(x.Address, excelCodeStatuses, MessageConstants.InvalidColor);
                        }
                    }
                }
                else
                {
                    if (x.ColumnNumber <= Constants.HeaderLength)
                        continue;
                }
                if (x.KpiInfo != Constants.KpiInfoText && !decimal.TryParse(x?.KpiValue?.Replace(Constants.KpiInfoMultiple, string.Empty)?.Replace(Constants.KpiInfoPercent, string.Empty)?.Replace(Constants.KpiInfoCurrency, string.Empty), NumberStyles.Float, CultureInfo.InvariantCulture, out decimal updatedValue))
                {
                    CreateCellError(x.Address, excelCodeStatuses, MessageConstants.CellInvalid);
                }
                if (x.KpiValue.Length > Constants.CellValueMaxLength && x.KpiInfo != Constants.KpiInfoText)
                {
                    CreateCellError(x.Address, excelCodeStatuses, MessageConstants.CellMaxLength);
                }
                if (x.IsMerged && x.KpiInfo != Constants.KpiInfoText)
                {
                    CreateCellError(x.Address, excelCodeStatuses, MessageConstants.InvalidMergedCells);
                }
                if (!excelCodeStatuses.Any(x => x.Code == Constants.error))
                {
                    pcKpiValueModels.Add(new PcKpiValueModel()
                    {
                        Quarter = x.Quarter,
                        Year = x.Year,
                        Half =x.Half,
                        Month = x.Month ?? null,
                        KpiId = x.KpiId,
                        KpiValue = x.KpiInfo == Constants.KpiInfoText ? x.KpiTextValue : GetFormattedValue(x.KpiValue, x.CellFormat),
                        KpiInfo = x.KpiInfo,
                        ValueTypeId = x.ValueTypeId,
                        ModuleId = x.ModuleId,
                        ColumnKpiId = x.ColumnKpiId,
                        ColumnNumber = x.ColumnNumber,
                        CellFormat = x.CellFormat,
                        IsMerged = x.IsMerged,
                        MergedColRange = string.IsNullOrEmpty(x.MergedColRange) ? x.MergedColRange : $"{colNo}-{x.MergedColRange}",
                    });
                }
            }
        }
        /// <summary>
        /// Formats the value of an <see cref="IXLCell"/> and returns it as a string.
        /// </summary>
        /// <param name="x">The <see cref="IXLCell"/> to format.</param>
        /// <param name="excelCodeStatuses">The list of <see cref="ExcelCodeStatus"/> objects.</param>
        /// <returns>The formatted value as a string.</returns>
        public static string FormattingValue(IXLCell x, List<ExcelCodeStatus> excelCodeStatuses)
        {
            x = ValidateValue(x);
            if (x.DataType == XLDataType.Number && !string.IsNullOrEmpty(x.Value?.ToString()))
            {
                try
                {
                    bool isDouble = x.TryGetValue(out double updatedValue);
                    var decimalValueWithDot = updatedValue.ToString().Split('.');
                    var updatedNumber = decimalValueWithDot.Length > 0 ? decimalValueWithDot[0] : updatedValue.ToString();
                    if ((x.Value.ToString().Contains("E") || x.Value.ToString().Contains("e")) && isDouble && updatedNumber?.Length > Constants.CellMaxLength)
                    {
                        BulkUploadHelper.CreateCellError(x.Address.ToString(), excelCodeStatuses, MessageConstants.ValuesInExponential);
                        return null;
                    }
                    return updatedValue.ToString();
                }
                catch (Exception ex)
                {
                    BulkUploadHelper.CreateCellError(x.Address.ToString(), excelCodeStatuses, MessageConstants.CellInvalid);
                }
            }
            return x.GetValue<string>()?.Trim();
        }

        /// <summary>
        /// Validates a cell value in an Excel worksheet.
        /// </summary>
        private static IXLCell ValidateValue(IXLCell x)
        {
            try
            {
                x.GetValue<string>()?.Trim();
            }
            catch (Exception ex)
            {
                var match = MyRegex().Match(x.GetFormattedString());
                if (match.Success)
                {
                    _ = double.TryParse(match.Value.ToString(), NumberStyles.Float, CultureInfo.InvariantCulture, out double val);
                    x.Value = val;
                }
            }
            return x;
        }

        [GeneratedRegex(@"[+-]?\d*\.?\d+(?:[Ee][+-]?\d+)?")]
        private static partial Regex MyRegex();
        private static HashSet<int> GetAliasNameValidateModuleTypes()
        {
            return new HashSet<int>
            {
                (int)KpiModuleType.ProfitAndLoss,
                (int)KpiModuleType.BalanceSheet,
                (int)KpiModuleType.CashFlow
            };
        }
        /// <summary>
        /// Checks for rows in the provided list that have a KpiId of 0 and creates a cell error for each.
        /// </summary>
        /// <param name="unmappedRowKpiList">The list of KpiFieldValueModel objects to check.</param>
        /// <param name="excelCodeStatuses">The list of ExcelCodeStatus objects to update with errors.</param>
        public static void CheckExtraRowsAnyExtraValues(List<KpiFieldValueModel> unmappedRowKpiList, List<ExcelCodeStatus> excelCodeStatuses)
        {
            unmappedRowKpiList.Where(x => x.KpiId == 0).ToList().ForEach(item =>
            {
                CreateCellError(item.Address, excelCodeStatuses, MessageConstants.KpiIdEmptyCellMessage);
            });
        }
        /// <summary>
        /// SetMonthlyReportCompanyColor
        /// </summary>
        /// <param name="worksheet"></param>
        /// <param name="bulkUploadDataModel"></param>
        /// <param name="excelCodeStatuses"></param>
        public static void SetMonthlyReportCompanyColor(IXLWorksheet worksheet, BulkUploadDataModel bulkUploadDataModel, List<ExcelCodeStatus> excelCodeStatuses)
        {
            int columnNumber = 0;
            columnNumber = GetColorColNumber(worksheet, columnNumber, excelCodeStatuses);
            if (columnNumber > 0)
            {
                var mergedRange = worksheet.MergedRanges
                                           .FirstOrDefault(range => range.FirstColumn().ColumnNumber() <= columnNumber && range.LastColumn().ColumnNumber() >= columnNumber);
                bulkUploadDataModel.ColorColNo = columnNumber;
                if (mergedRange != null)
                {
                    bulkUploadDataModel.Color = mergedRange.FirstCell()?.Value?.ToString();
                    if (string.IsNullOrEmpty(bulkUploadDataModel.Color))
                    {
                        excelCodeStatuses.Add(new ExcelCodeStatus()
                        {
                            Code = "error",
                            CellCode = mergedRange.FirstCell()?.Address?.ColumnLetter + mergedRange.FirstCell()?.Address?.RowNumber,
                            Message = MessageConstants.ColorValueMissing
                        });
                    }
                }
            }
        }
        /// <summary>
        /// GetColorColNumber
        /// </summary>
        /// <param name="worksheet"></param>
        /// <param name="columnNumber"></param>
        /// <param name="excelCodeStatuses"></param>
        /// <returns></returns>
        public static int GetColorColNumber(IXLWorksheet worksheet, int columnNumber, List<ExcelCodeStatus> excelCodeStatuses)
        {
            var colorColumn = worksheet.ColumnsUsed().FirstOrDefault(column => column.FirstCell().GetValue<string>()?.ToLower() == Constants.Color);
            if (colorColumn == null)
            {
                excelCodeStatuses.Add(new ExcelCodeStatus()
                {
                    Code = "error",
                    CellCode = string.Empty,
                    Message = MessageConstants.MandatoryColumnsMissing
                });
            }
            return colorColumn?.FirstCell().Address.ColumnNumber ?? columnNumber;
        }

    }
}
