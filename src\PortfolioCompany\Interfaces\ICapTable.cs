using Contract.CapTable;
using Contract.Funds;
using System.Threading.Tasks;

namespace PortfolioCompany.Interfaces
{
    public interface ICapTable
    {
       Task<PcCapTable> GetCapTableConfig(int companyId, int subPageId = (int)PageConfigurationSubFeature.CapTable);
       Task<PcCapTableResponse>GetCapTableValues(PcCapTableFilterType pcCapTableFilter);
       Task<CapTableExport> GetCapTableExportValues(PcCapTableFilterType pcCapTableFilter);
    }
}