using System;
using System.Threading.Tasks;
using Contract.CapTable;
using ManagedAccounts.Models.Results;

namespace ManagedAccounts.Interfaces
{
    /// <summary>
    /// Service interface for managed account cap table operations
    /// </summary>
    public interface IManagedCapTableService
    {
        /// <summary>
        /// Gets the cap table configuration for a managed account
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="moduleName">The module name</param>
        /// <returns>The cap table configuration</returns>
        Task<ManagedCapTableConfigResponse> GetCapTableConfigAsync(Guid managedAccountId, string moduleName);

        /// <summary>
        /// Gets the cap table values for a managed account
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="periodId">The period ID</param>
        /// <param name="isMonthly">Whether to include monthly data</param>
        /// <param name="isQuarterly">Whether to include quarterly data</param>
        /// <param name="isAnnually">Whether to include annually data</param>
        /// <param name="moduleName">The module name</param>
        /// <returns>The cap table values</returns>
        Task<PcCapTableResponse> GetCapTableValuesAsync(
            Guid managedAccountId, 
            int periodId, 
            bool isMonthly, 
            bool isQuarterly, 
            bool isAnnually, 
            string moduleName);
    }
}
