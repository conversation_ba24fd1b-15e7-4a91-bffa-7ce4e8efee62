﻿using API.Filters.CustomAuthorization;
using System.Net;
using System.Threading.Tasks;
using API.Helpers;
using Contract.KPI;
using Contract.Utility;
using Microsoft.AspNetCore.Mvc;
using Utility.Resource;
using Contract.Account;
using ManagedAccounts.Interfaces;
using System;
using System.Linq;
using MediatR;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Commands;
using Microsoft.AspNetCore.Http;

namespace API.Controllers.ManagedAccounts
{
    public class MAKPIController : BaseController
    {
        private readonly IKpiService _kpiService;
        private readonly IManagedAccountDetailsService _managedAccountDetailsService;
        private readonly IMediator _mediator;
        public MAKPIController(IKpiService kPIService, IManagedAccountDetailsService mAService, IMediator mediator, IInjectedParameters InjectedParameters, IHelperService helperService) : base(InjectedParameters, helperService)
        {
            _kpiService = kPIService;
            _managedAccountDetailsService = mAService;
            _mediator = mediator;
        }

        [Route("managed-accounts/kpi/mapping/{Id}/{Type}/{moduleID}")]
        [UserFeatureAuthorize((int)Features.KPIsMapping)]
        [HttpGet]
        public async Task<IActionResult> GetKPIMapping(Guid Id, string Type, int moduleID)
        {
            if (Id.Equals(Guid.Empty) || string.IsNullOrWhiteSpace(Type)) return BadRequest();
            
            var mAdetails = await _managedAccountDetailsService.GetByIdAsync(Id);
            
            if(mAdetails == null)
                return BadRequest();

            var result = await _kpiService.GetKPIMapping(mAdetails.UAMId, Type, moduleID);
            return result == null ? JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound) : JsonResponse.Create(HttpStatusCode.OK, result);
        }
        [HttpGet("managed-accounts/kpi/unmapped/{Id}/{Type}/{moduleID}")]
        [UserFeatureAuthorize((int)Features.KPIsMapping)]
        public async Task<IActionResult> GetUnMappedKpi(Guid Id, string Type, int moduleID)
        {
            if (Id.Equals(Guid.Empty) || string.IsNullOrWhiteSpace(Type)) return BadRequest();

            var mAdetails = await _managedAccountDetailsService.GetByIdAsync(Id);

            if (mAdetails == null)
                return BadRequest();
            var result = await _kpiService.GetUnMappedKpi(mAdetails.UAMId, Type, moduleID);
            return Ok(result);
        }

        [HttpPost("managed-accounts/kpi/mapping/{Id}/{type}/{moduleID}")]
        [UserFeatureAuthorize((int)Features.KPIsMapping)]
        public async Task<IActionResult> UpdateKPIMapping(Guid Id, string Type, KpiMappingQueryModel KpiMappingQueryModel, int moduleID)
        {
            if (Id.Equals(Guid.Empty) || string.IsNullOrWhiteSpace(Type)) return BadRequest();

            var mAdetails = await _managedAccountDetailsService.GetByIdAsync(Id);

            if (mAdetails == null)
                return BadRequest();

            await _kpiService.UpdateKPIMapping(mAdetails.UAMId, Type, KpiMappingQueryModel?.KPIMappings, GetCurrentUserId(), moduleID);
            if (KpiMappingQueryModel?.CompanyIds?.Any() == true)
            {
                await _kpiService.CopyKPIToCompanies(new CopyToKpiQueryModel()
                {
                    CompanyId = mAdetails.UAMId,
                    KpiType = Type == "Financial KPIs" ? "Trading Records" : Type,
                    CompanyIds = KpiMappingQueryModel.CompanyIds,
                    UserId = GetCurrentUserId(),
                    ModuleId = moduleID
                });
            }

            return JsonResponse.Create(HttpStatusCode.OK, "Done");
        }

        /// <summary>
        /// Downloads an Excel template for Managed Account Cap Table data import
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="moduleName">The module ID for KPI filtering</param>
        /// <returns>An Excel file containing the managed account cap table template</returns>
        [HttpPost("managed-accounts/import/template")]
        [UserFeatureAuthorize((int)Features.ManagedAccounts)]
        public async Task<IActionResult> DownloadManagedCapTableTemplate(DownloadManagedCapTableTemplateQuery query)
        {
            if (query.ManagedAccountId == Guid.Empty || string.IsNullOrEmpty(query.ModuleName))
            {
                return BadRequest("Invalid managed account ID or module ID");
            }

            query.UserId = GetCurrentUserId();

            var result = await _mediator.Send(query);

            if (result.IsSuccess)
            {
                return new ExcelContentResult(result.FileName, result.FilePath);
            }

            return BadRequest(result.ErrorMessage);
        }

        /// <summary>
        /// Uploads and processes an Excel file for managed account data import
        /// </summary>
        /// <param name="file">The Excel file to upload</param>
        /// <param name="moduleName">The module name for processing</param>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <returns>Processing status and results</returns>
        [HttpPost("managed-accounts/import/excel")]
        [UserFeatureAuthorize((int)Features.ManagedAccounts)]
        public async Task<IActionResult> UploadManagedAccountExcel(UploadManagedAccountExcelCommand command)
        {
            command.UserId = GetCurrentUserId();
            command.Connection = AwsSecretsManagerHelper.GetConnectionString();

            var result = await _mediator.Send(command);

            if (result.IsSuccess)
            {
                return JsonResponse.Create(HttpStatusCode.OK, result.StatusList);
            }

            return JsonResponse.Create(HttpStatusCode.BadRequest, result.StatusList);
        }

    }
}
