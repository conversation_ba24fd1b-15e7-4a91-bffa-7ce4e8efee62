namespace API.Controllers.CapTable
{
    using System.Threading.Tasks;
    using API.Filters.CustomAuthorization;
    using API.Helpers;
    using Contract.Account;
    using Contract.CapTable;
    using Contract.Funds;
    using Contract.Utility;
    using DataAnalytic.Models;
    using DataAnalytic.Services;
    using global::PortfolioCompany.Interfaces;
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.Extensions.Logging;
    [Route("api")]
    [ApiController]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    public class CapTableController(ICapTable capTable, ILogger<CapTableController> logger,IEncryption encryption, IAnalyticCapTableService analyticCapTable) : ControllerBase
    {
        private readonly ICapTable _capTable = capTable;
        private readonly ILogger<CapTableController> _logger = logger;
        private readonly IEncryption _encryption = encryption;
        private readonly IAnalyticCapTableService analyticCapTable = analyticCapTable;
        /// <summary>
        /// Retrieves the cap table configuration.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation. The task result is an <see cref="IActionResult"/>.</returns>
        [HttpGet("cap-table/config/{encryptedCompanyId}")]
        [UserFeatureAuthorize((int)Features.PortfolioCompany)]
        public async Task<IActionResult> GetCapTableConfig(string encryptedCompanyId)
        {
            _ = int.TryParse(_encryption.Decrypt(encryptedCompanyId), out int companyId);
            var result = await _capTable.GetCapTableConfig(companyId);
            if (result != null)
            {
                _logger.LogInformation("Successfully retrieved cap table configuration with data for company ID: {CompanyId}", companyId);
                return Ok(result);
            }
            else
            {
                _logger.LogInformation("No cap table configuration data found for company ID: {CompanyId}", companyId);
                return NotFound();
            }
        }
        /// <summary>
        /// Retrieves the cap table values for a portfolio company based on the specified filter.
        /// </summary>
        /// <param name="pcCapTableFilter">The filter to apply when retrieving the cap table values.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation. The task result is an <see cref="IActionResult"/> containing the cap table values.</returns>
        [HttpPost("cap-table/values")]
        [UserFeatureAuthorize((int)Features.PortfolioCompany)]
        public async Task<IActionResult> GetCapTableValues(PcCapTableFilterType pcCapTableFilter)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var result = await _capTable.GetCapTableValues(pcCapTableFilter);
            int companyId = pcCapTableFilter.CompanyId;
            if (result != null)
            {
                _logger.LogInformation("Successfully retrieved cap table values for company ID: {CompanyId}", companyId);
                return Ok(result);
            }
            else
            {
                _logger.LogInformation("No cap table values found for company ID: {CompanyId}", companyId);
                return NoContent();
            }
        }
        [HttpPost("data-analytics/cap-table")]
        public async Task<IActionResult> GetCapTableAnalyticValues(CapTableFilter pcCapTableFilter)
        {
            return Ok(await analyticCapTable.GetCapTableAnalyticValues(pcCapTableFilter));
        }
        /// <summary>
        /// GetOtherCapTableConfig
        /// </summary>
        /// <param name="encryptedCompanyId"></param>
        /// <returns></returns>
        [HttpGet("othercap-table/config/{encryptedCompanyId}")]
        [UserFeatureAuthorize((int)Features.PortfolioCompany)]
        public async Task<IActionResult> GetOtherCapTableConfig(string encryptedCompanyId)
        {
            _ = int.TryParse(_encryption.Decrypt(encryptedCompanyId), out int companyId);
            var result = await _capTable.GetCapTableConfig(companyId, (int)PageConfigurationSubFeature.OtherCapTable);
            if (result != null)
            {
                _logger.LogInformation("Successfully retrieved other cap table configuration with data for company ID: {CompanyId}", companyId);
                return Ok(result);
            }
            else
            {
                _logger.LogInformation("No other cap table configuration data found for company ID: {CompanyId}", companyId);
                return NotFound();
            }
        }
    }
}