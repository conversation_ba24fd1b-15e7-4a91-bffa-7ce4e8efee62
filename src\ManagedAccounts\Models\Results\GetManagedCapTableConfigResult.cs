using System.Collections.Generic;
using Contract.CapTable;

namespace ManagedAccounts.Models.Results
{
    /// <summary>
    /// Result for managed cap table configuration query
    /// </summary>
    public class GetManagedCapTableConfigResult
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// The managed cap table configuration data
        /// </summary>
        public ManagedCapTableConfigResponse? ConfigData { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        /// <param name="configData">The configuration data</param>
        /// <returns>Successful result</returns>
        public static GetManagedCapTableConfigResult Success(ManagedCapTableConfigResponse configData)
        {
            return new GetManagedCapTableConfigResult
            {
                IsSuccess = true,
                ConfigData = configData
            };
        }

        /// <summary>
        /// Creates a failed result
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <returns>Failed result</returns>
        public static GetManagedCapTableConfigResult Failure(string errorMessage)
        {
            return new GetManagedCapTableConfigResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// Response model for managed cap table configuration
    /// </summary>
    public class ManagedCapTableConfigResponse
    {
        /// <summary>
        /// List of cap table periods for the managed account
        /// </summary>
        public List<CapTablePeriod> CapTablePeriods { get; set; } = new();

        /// <summary>
        /// Additional configuration data for future extensibility
        /// </summary>
        public string? AdditionalConfigData { get; set; }

        /// <summary>
        /// Latest period information
        /// </summary>
        public CapTablePeriod? LatestPeriod { get; set; }
    }
}
