﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="11.1.0" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.1.0" />
    <PackageReference Include="FluentValidation" Version="11.8.1" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.2" />
    <PackageReference Include="ClosedXML" Version="0.96.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Shared.csproj" />
    <ProjectReference Include="..\DataAccessLayer\DataAccessLayer.csproj" />
    <ProjectReference Include="..\Exports\Exports.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Handlers\Commands\" />
	<Folder Include="Handlers\Queries\" />
    <Folder Include="Models\Results\" />
    <Folder Include="Validators\" />
  </ItemGroup>

</Project>
