using System;
using System.Collections.Generic;
using System.Linq;
using Contract.CapTable;
using ManagedAccounts.Models.Results;

namespace ManagedAccounts.Helpers
{
    /// <summary>
    /// Static helper class for managed cap table operations
    /// </summary>
    public static class ManagedCapTableHelper
    {
        /// <summary>
        /// Maps managed account ID to portfolio company ID for cap table operations
        /// This is a placeholder - actual implementation should query the database
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <returns>The corresponding portfolio company ID</returns>
        public static int GetPortfolioCompanyIdFromManagedAccount(Guid managedAccountId)
        {
            // TODO: Implement actual mapping logic from database
            // This should query the ManagedAccount table to get the corresponding PortfolioCompanyId
            throw new NotImplementedException("Mapping from ManagedAccountId to PortfolioCompanyId needs to be implemented");
        }

        /// <summary>
        /// Gets the module ID from module name
        /// This is a placeholder - actual implementation should query the database
        /// </summary>
        /// <param name="moduleName">The module name</param>
        /// <returns>The module ID</returns>
        public static int GetModuleIdFromName(string moduleName)
        {
            // TODO: Implement actual module name to ID mapping
            // This should query the appropriate table to get ModuleId from ModuleName
            throw new NotImplementedException("Module name to ID mapping needs to be implemented");
        }

        /// <summary>
        /// Creates a managed cap table configuration response from cap table periods
        /// </summary>
        /// <param name="periods">The cap table periods</param>
        /// <returns>The managed cap table configuration response</returns>
        public static ManagedCapTableConfigResponse CreateConfigResponse(List<CapTablePeriod> periods)
        {
            if (periods == null || !periods.Any())
            {
                return new ManagedCapTableConfigResponse
                {
                    CapTablePeriods = new List<CapTablePeriod>(),
                    LatestPeriod = null
                };
            }

            // Find the latest period (assuming higher PeriodId means more recent)
            var latestPeriod = periods.OrderByDescending(p => p.PeriodId).FirstOrDefault();

            return new ManagedCapTableConfigResponse
            {
                CapTablePeriods = periods.OrderBy(p => p.PeriodId).ToList(),
                LatestPeriod = latestPeriod
            };
        }

        /// <summary>
        /// Validates the managed cap table values query parameters
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="periodId">The period ID</param>
        /// <param name="moduleName">The module name</param>
        /// <returns>Validation error message or null if valid</returns>
        public static string? ValidateCapTableValuesQuery(Guid managedAccountId, int periodId, string moduleName)
        {
            if (managedAccountId == Guid.Empty)
            {
                return "ManagedAccountId cannot be empty";
            }

            if (periodId <= 0)
            {
                return "PeriodId must be greater than 0";
            }

            if (string.IsNullOrWhiteSpace(moduleName))
            {
                return "ModuleName cannot be null or empty";
            }

            return null;
        }

        /// <summary>
        /// Validates the managed cap table config query parameters
        /// </summary>
        /// <param name="managedAccountId">The managed account ID</param>
        /// <param name="moduleName">The module name</param>
        /// <returns>Validation error message or null if valid</returns>
        public static string? ValidateCapTableConfigQuery(Guid managedAccountId, string moduleName)
        {
            if (managedAccountId == Guid.Empty)
            {
                return "ManagedAccountId cannot be empty";
            }

            if (string.IsNullOrWhiteSpace(moduleName))
            {
                return "ModuleName cannot be null or empty";
            }

            return null;
        }
    }
}
