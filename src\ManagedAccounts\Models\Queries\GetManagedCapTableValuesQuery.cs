using System;
using ManagedAccounts.Models.Results;
using MediatR;

namespace ManagedAccounts.Models.Queries
{
    /// <summary>
    /// Query for getting managed account cap table values
    /// </summary>
    public class GetManagedCapTableValuesQuery : IRequest<GetManagedCapTableValuesResult>
    {
        /// <summary>
        /// The ID of the managed account
        /// </summary>
        public Guid ManagedAccountId { get; set; }

        /// <summary>
        /// The period ID for filtering values
        /// </summary>
        public int PeriodId { get; set; }

        /// <summary>
        /// Whether to include monthly data
        /// </summary>
        public bool IsMonthly { get; set; }

        /// <summary>
        /// Whether to include quarterly data
        /// </summary>
        public bool IsQuarterly { get; set; }

        /// <summary>
        /// Whether to include annually data
        /// </summary>
        public bool IsAnnually { get; set; }

        /// <summary>
        /// The module name for filtering values
        /// </summary>
        public required string ModuleName { get; set; }
    }
}
