using MediatR;
using Microsoft.Extensions.Logging;
using ManagedAccounts.Models.Commands;
using ManagedAccounts.Interfaces;
using Contract.Utility;
using Imports.Kpi;
using Contract.BulkUpload;
using Shared;

namespace ManagedAccounts.Handlers.Commands
{
    /// <summary>
    /// Handler for processing Excel file uploads for managed accounts
    /// </summary>
    public class UploadManagedAccountExcelCommandHandler : IRequestHandler<UploadManagedAccountExcelCommand, UploadManagedAccountExcelResult>
    {
        private readonly IManagedAccountDetailsService _managedAccountDetailsService;
        private readonly IBulkUpload _bulkUploadServie;
        private readonly ILogger<UploadManagedAccountExcelCommandHandler> _logger;

        public UploadManagedAccountExcelCommandHandler(
            IManagedAccountDetailsService managedAccountDetailsService,
            IBulkUpload bulkUploadService,
            ILogger<UploadManagedAccountExcelCommandHandler> logger)
        {
            _managedAccountDetailsService = managedAccountDetailsService;
            _logger = logger;
            _bulkUploadServie = bulkUploadService;
        }

        /// <summary>
        /// Handles the Excel upload and processing command
        /// </summary>
        /// <param name="request">The upload command</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Upload result</returns>
        public async Task<UploadManagedAccountExcelResult> Handle(UploadManagedAccountExcelCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Starting Excel upload processing for managed account: {ManagedAccountId}, Module: {ModuleName}", 
                    request.ManagedAccountId, request.ModuleName);

                // Validate input
                var validationResult = ValidateRequest(request);
                if (!validationResult.IsValid)
                {
                    return UploadManagedAccountExcelResult.Failure(validationResult.ErrorMessage, new List<Status>());
                }

                // Verify managed account exists
                var managedAccountDetails = await _managedAccountDetailsService.GetByIdAsync(request.ManagedAccountId);
                if (managedAccountDetails == null)
                {
                    return UploadManagedAccountExcelResult.Failure("Managed account not found", new List<Status>());
                }

                // Create temporary directory for file processing
                string folderName = Guid.NewGuid().ToString();
                string webRootPath = Path.Combine(Directory.GetCurrentDirectory(), "TempUploads", folderName);
                
                if (!Directory.Exists(webRootPath))
                {
                    Directory.CreateDirectory(webRootPath);
                }

                try
                {
                    // Save file to temporary location
                    var extension = Path.GetExtension(request.File.FileName).ToLower();
                    string fileName = $"{Guid.NewGuid()}{extension}";
                    string fullPath = Path.Combine(webRootPath, fileName);
                    
                    using (var stream = new FileStream(fullPath, FileMode.Create))
                    {
                        await request.File.CopyToAsync(stream, cancellationToken);
                    }

                    var bulkdataModel = new BulkUploadDataModel() { 
                         ModuleName = request.ModuleName,
                         Path = fullPath,
                         UserID = request.UserId,
                         PortfolioCompanyID = managedAccountDetails.UAMId,
                         Connection = request.Connection,
                         
                    };

                    var status = await _bulkUploadServie.ProcessManagedCapTableKpi(bulkdataModel, request.ModuleName);

                    return status.Any(x => x.Code == Constants.error) ? UploadManagedAccountExcelResult.Failure("Data upload fail", status.Where(x => x.Code == Constants.error).ToList()) : UploadManagedAccountExcelResult.Success(status, 0);
                }
                finally
                {
                    // Clean up temporary files
                    if (Directory.Exists(webRootPath))
                    {
                        try
                        {
                            Directory.Delete(webRootPath, true);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to clean up temporary directory: {WebRootPath}", webRootPath);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Excel upload for managed account: {ManagedAccountId}", request.ManagedAccountId);
                return UploadManagedAccountExcelResult.Failure($"Error processing Excel file: {ex.Message}", new List<Status>());
            }
        }

        /// <summary>
        /// Validates the upload request
        /// </summary>
        /// <param name="request">The upload request</param>
        /// <returns>Validation result</returns>
        private static (bool IsValid, string ErrorMessage) ValidateRequest(UploadManagedAccountExcelCommand request)
        {
            if (request.File == null || request.File.Length == 0)
            {
                return (false, "No file uploaded or file is empty");
            }

            if (request.ManagedAccountId == Guid.Empty)
            {
                return (false, "Invalid managed account ID");
            }

            if (string.IsNullOrWhiteSpace(request.ModuleName))
            {
                return (false, "Module name is required");
            }

            // Validate file type
            var extension = Path.GetExtension(request.File.FileName).ToLower();
            var validFileTypes = new[] { ".xlsx", ".xls" };
            if (!validFileTypes.Contains(extension))
            {
                return (false, "Invalid file format. Only Excel files (.xlsx, .xls) are allowed");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// Extracts the number of processed rows from status list
        /// </summary>
        /// <param name="statusList">List of processing statuses</param>
        /// <returns>Number of processed rows</returns>
        private static int ExtractProcessedRowCount(List<Status> statusList)
        {
            // Look for status messages that contain row count information
            var infoStatus = statusList.FirstOrDefault(s => s.Code == "INFO" && s.Message.Contains("data rows"));
            if (infoStatus != null)
            {
                // Try to extract number from message like "Processing 5 data rows for module: ModuleName"
                var words = infoStatus.Message.Split(' ');
                for (int i = 0; i < words.Length - 1; i++)
                {
                    if (words[i].Equals("Processing", StringComparison.OrdinalIgnoreCase) && 
                        int.TryParse(words[i + 1], out int count))
                    {
                        return count;
                    }
                }
            }
            
            return 0;
        }
    }
}
