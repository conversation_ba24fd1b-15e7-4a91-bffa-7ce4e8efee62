using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contract.CapTable;
using DapperRepository;
using DataAccessLayer.UnitOfWork;
using ManagedAccounts.Models.Results;
using ManagedAccounts.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ManagedAccounts.UnitTest
{
    public class ManagedCapTableServiceTests : IDisposable
    {
        private readonly Mock<ILogger<ManagedCapTableService>> _mockLogger;
        private readonly Mock<IDapperGenericRepository> _mockDapperRepository;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly ManagedCapTableService _service;

        public ManagedCapTableServiceTests()
        {
            _mockLogger = new Mock<ILogger<ManagedCapTableService>>();
            _mockDapperRepository = new Mock<IDapperGenericRepository>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _service = new ManagedCapTableService(_mockLogger.Object, _mockDapperRepository.Object, _mockUnitOfWork.Object);
        }

        [Fact]
        public async Task GetCapTableConfigAsync_ValidInput_ReturnsConfiguration()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            var moduleName = "TestModule";
            var expectedPeriods = new List<CapTablePeriod>
            {
                new CapTablePeriod { PeriodId = 1, Period = "2023", ModuleId = 1, IsAnnually = true },
                new CapTablePeriod { PeriodId = 2, Period = "2024", ModuleId = 1, IsAnnually = true }
            };

            var managedAccountDetails = new List<DataAccessLayer.ManagedAccounts.ManagedAccountDetails>
            {
                new DataAccessLayer.ManagedAccounts.ManagedAccountDetails
                {
                    ManagedAccountID = managedAccountId,
                    UAMId = 123,
                    ManagedAccountName = "Test Account",
                    IsDeleted = false
                }
            };

            _mockDapperRepository.Setup(x => x.Query<DataAccessLayer.ManagedAccounts.ManagedAccountDetails>(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(managedAccountDetails);

            _mockDapperRepository.Setup(x => x.Query<CapTablePeriod>(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(expectedPeriods);

            // Act
            var result = await _service.GetCapTableConfigAsync(managedAccountId, moduleName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.CapTablePeriods.Count);
            Assert.Equal(expectedPeriods.OrderByDescending(p => p.PeriodId).First(), result.LatestPeriod);
        }

        [Fact]
        public async Task GetCapTableConfigAsync_EmptyManagedAccountId_ThrowsArgumentException()
        {
            // Arrange
            var managedAccountId = Guid.Empty;
            var moduleName = "TestModule";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _service.GetCapTableConfigAsync(managedAccountId, moduleName));
            
            Assert.Contains("ManagedAccountId cannot be empty", exception.Message);
        }

        [Fact]
        public async Task GetCapTableConfigAsync_NullModuleName_ThrowsArgumentException()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            string moduleName = null;

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _service.GetCapTableConfigAsync(managedAccountId, moduleName));
            
            Assert.Contains("ModuleName cannot be null or empty", exception.Message);
        }

        [Fact]
        public async Task GetCapTableConfigAsync_NoManagedAccountFound_ReturnsEmptyConfiguration()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            var moduleName = "TestModule";

            _mockDapperRepository.Setup(x => x.Query<DataAccessLayer.ManagedAccounts.ManagedAccountDetails>(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new List<DataAccessLayer.ManagedAccounts.ManagedAccountDetails>());

            // Act
            var result = await _service.GetCapTableConfigAsync(managedAccountId, moduleName);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.CapTablePeriods);
            Assert.Null(result.LatestPeriod);
        }

        [Fact]
        public async Task GetCapTableValuesAsync_ValidInput_ReturnsCapTableResponse()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            var periodId = 1;
            var moduleName = "TestModule";
            var isMonthly = true;
            var isQuarterly = false;
            var isAnnually = false;

            var managedAccountDetails = new List<DataAccessLayer.ManagedAccounts.ManagedAccountDetails>
            {
                new DataAccessLayer.ManagedAccounts.ManagedAccountDetails
                {
                    ManagedAccountID = managedAccountId,
                    UAMId = 123,
                    ManagedAccountName = "Test Account",
                    IsDeleted = false
                }
            };

            var mockMultipleData = new Mock<IMultipleResultReader>();
            mockMultipleData.SetupSequence(x => x.ReadAsync<CapTableValue>())
                .ReturnsAsync(new List<CapTableValue>());
            mockMultipleData.SetupSequence(x => x.ReadAsync<CapTableColumnModel>())
                .ReturnsAsync(new List<CapTableColumnModel>());
            mockMultipleData.SetupSequence(x => x.ReadAsync<CapTableParentModel>())
                .ReturnsAsync(new List<CapTableParentModel>());

            _mockDapperRepository.Setup(x => x.Query<DataAccessLayer.ManagedAccounts.ManagedAccountDetails>(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(managedAccountDetails);

            _mockDapperRepository.Setup(x => x.QueryMultipleDataAsync(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(mockMultipleData.Object);

            // Act
            var result = await _service.GetCapTableValuesAsync(managedAccountId, periodId, isMonthly, isQuarterly, isAnnually, moduleName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(isMonthly, result.IsMonthly);
            Assert.Equal(isQuarterly, result.IsQuarterly);
            Assert.Equal(isAnnually, result.IsAnnually);
        }

        [Fact]
        public async Task GetCapTableValuesAsync_InvalidPeriodId_ThrowsArgumentException()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            var periodId = 0; // Invalid
            var moduleName = "TestModule";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _service.GetCapTableValuesAsync(managedAccountId, periodId, true, false, false, moduleName));
            
            Assert.Contains("PeriodId must be greater than 0", exception.Message);
        }

        [Fact]
        public async Task GetCapTableValuesAsync_EmptyModuleName_ThrowsArgumentException()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            var periodId = 1;
            var moduleName = ""; // Empty

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _service.GetCapTableValuesAsync(managedAccountId, periodId, true, false, false, moduleName));
            
            Assert.Contains("ModuleName cannot be null or empty", exception.Message);
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
