using System;
using System.Threading;
using System.Threading.Tasks;
using API.Controllers.ManagedAccounts;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace ManagedAccounts.UnitTest
{
    public class ManagedCapTableControllerTests : IDisposable
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly Mock<ILogger<ManagedCapTableController>> _mockLogger;
        private readonly ManagedCapTableController _controller;

        public ManagedCapTableControllerTests()
        {
            _mockMediator = new Mock<IMediator>();
            _mockLogger = new Mock<ILogger<ManagedCapTableController>>();
            _controller = new ManagedCapTableController(_mockMediator.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task GetManagedCapTableConfig_ValidInput_ReturnsOkResult()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            var moduleName = "TestModule";
            var expectedConfig = new ManagedCapTableConfigResponse();
            var expectedResult = GetManagedCapTableConfigResult.Success(expectedConfig);

            _mockMediator.Setup(x => x.Send(It.IsAny<GetManagedCapTableConfigQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetManagedCapTableConfig(managedAccountId, moduleName);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expectedConfig, okResult.Value);
        }

        [Fact]
        public async Task GetManagedCapTableConfig_EmptyManagedAccountId_ReturnsBadRequest()
        {
            // Arrange
            var managedAccountId = Guid.Empty;
            var moduleName = "TestModule";

            // Act
            var result = await _controller.GetManagedCapTableConfig(managedAccountId, moduleName);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errorResponse = badRequestResult.Value;
            Assert.NotNull(errorResponse);
        }

        [Fact]
        public async Task GetManagedCapTableConfig_NullModuleName_ReturnsBadRequest()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            string moduleName = null;

            // Act
            var result = await _controller.GetManagedCapTableConfig(managedAccountId, moduleName);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errorResponse = badRequestResult.Value;
            Assert.NotNull(errorResponse);
        }

        [Fact]
        public async Task GetManagedCapTableConfig_ServiceReturnsFailure_ReturnsNotFound()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            var moduleName = "TestModule";
            var expectedResult = GetManagedCapTableConfigResult.Failure("Configuration not found");

            _mockMediator.Setup(x => x.Send(It.IsAny<GetManagedCapTableConfigQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetManagedCapTableConfig(managedAccountId, moduleName);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var errorResponse = notFoundResult.Value;
            Assert.NotNull(errorResponse);
        }

        [Fact]
        public async Task GetManagedCapTableConfig_ServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var managedAccountId = Guid.NewGuid();
            var moduleName = "TestModule";

            _mockMediator.Setup(x => x.Send(It.IsAny<GetManagedCapTableConfigQuery>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetManagedCapTableConfig(managedAccountId, moduleName);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        [Fact]
        public async Task GetManagedCapTableValues_ValidInput_ReturnsOkResult()
        {
            // Arrange
            var query = new GetManagedCapTableValuesQuery
            {
                ManagedAccountId = Guid.NewGuid(),
                PeriodId = 1,
                IsMonthly = true,
                IsQuarterly = false,
                IsAnnually = false,
                ModuleName = "TestModule"
            };

            var expectedCapTableData = new Contract.CapTable.PcCapTableResponse();
            var expectedResult = GetManagedCapTableValuesResult.Success(expectedCapTableData);

            _mockMediator.Setup(x => x.Send(It.IsAny<GetManagedCapTableValuesQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetManagedCapTableValues(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expectedCapTableData, okResult.Value);
        }

        [Fact]
        public async Task GetManagedCapTableValues_ServiceReturnsFailure_ReturnsNotFound()
        {
            // Arrange
            var query = new GetManagedCapTableValuesQuery
            {
                ManagedAccountId = Guid.NewGuid(),
                PeriodId = 1,
                IsMonthly = true,
                IsQuarterly = false,
                IsAnnually = false,
                ModuleName = "TestModule"
            };

            var expectedResult = GetManagedCapTableValuesResult.Failure("Values not found");

            _mockMediator.Setup(x => x.Send(It.IsAny<GetManagedCapTableValuesQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetManagedCapTableValues(query);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var errorResponse = notFoundResult.Value;
            Assert.NotNull(errorResponse);
        }

        [Fact]
        public async Task GetManagedCapTableValues_ServiceThrowsException_ReturnsInternalServerError()
        {
            // Arrange
            var query = new GetManagedCapTableValuesQuery
            {
                ManagedAccountId = Guid.NewGuid(),
                PeriodId = 1,
                IsMonthly = true,
                IsQuarterly = false,
                IsAnnually = false,
                ModuleName = "TestModule"
            };

            _mockMediator.Setup(x => x.Send(It.IsAny<GetManagedCapTableValuesQuery>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetManagedCapTableValues(query);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}
