using System;
using System.Threading;
using System.Threading.Tasks;
using ManagedAccounts.Interfaces;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using MediatR;
using Microsoft.Extensions.Logging;

namespace ManagedAccounts.Handlers.Queries
{
    /// <summary>
    /// Handler for getting managed account cap table values
    /// </summary>
    public class GetManagedCapTableValuesHandler : IRequestHandler<GetManagedCapTableValuesQuery, GetManagedCapTableValuesResult>
    {
        private readonly IManagedCapTableService _managedCapTableService;
        private readonly ILogger<GetManagedCapTableValuesHandler> _logger;

        public GetManagedCapTableValuesHandler(
            IManagedCapTableService managedCapTableService,
            ILogger<GetManagedCapTableValuesHandler> logger)
        {
            _managedCapTableService = managedCapTableService ?? throw new ArgumentNullException(nameof(managedCapTableService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the get managed cap table values query
        /// </summary>
        /// <param name="request">The values query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public async Task<GetManagedCapTableValuesResult> Handle(GetManagedCapTableValuesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting managed cap table values for account: {ManagedAccountId}, period: {PeriodId}, module: {ModuleName}", 
                    request.ManagedAccountId, request.PeriodId, request.ModuleName);

                var capTableData = await _managedCapTableService.GetCapTableValuesAsync(
                    request.ManagedAccountId,
                    request.PeriodId,
                    request.IsMonthly,
                    request.IsQuarterly,
                    request.IsAnnually,
                    request.ModuleName);

                if (capTableData != null)
                {
                    _logger.LogInformation("Successfully retrieved managed cap table values for account: {ManagedAccountId}", 
                        request.ManagedAccountId);
                    return GetManagedCapTableValuesResult.Success(capTableData);
                }

                _logger.LogWarning("No managed cap table values found for account: {ManagedAccountId}, period: {PeriodId}, module: {ModuleName}", 
                    request.ManagedAccountId, request.PeriodId, request.ModuleName);
                return GetManagedCapTableValuesResult.Failure($"No cap table values found for managed account {request.ManagedAccountId}, period {request.PeriodId}, and module {request.ModuleName}");
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid arguments for managed cap table values query: {ManagedAccountId}, {PeriodId}, {ModuleName}", 
                    request.ManagedAccountId, request.PeriodId, request.ModuleName);
                return GetManagedCapTableValuesResult.Failure(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting managed cap table values for account: {ManagedAccountId}, period: {PeriodId}, module: {ModuleName}", 
                    request.ManagedAccountId, request.PeriodId, request.ModuleName);
                return GetManagedCapTableValuesResult.Failure("An unexpected error occurred while retrieving the cap table values");
            }
        }
    }
}
