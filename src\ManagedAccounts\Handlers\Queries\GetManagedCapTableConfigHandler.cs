using System;
using System.Threading;
using System.Threading.Tasks;
using ManagedAccounts.Interfaces;
using ManagedAccounts.Models.Queries;
using ManagedAccounts.Models.Results;
using MediatR;
using Microsoft.Extensions.Logging;

namespace ManagedAccounts.Handlers.Queries
{
    /// <summary>
    /// Handler for getting managed account cap table configuration
    /// </summary>
    public class GetManagedCapTableConfigHandler : IRequestHandler<GetManagedCapTableConfigQuery, GetManagedCapTableConfigResult>
    {
        private readonly IManagedCapTableService _managedCapTableService;
        private readonly ILogger<GetManagedCapTableConfigHandler> _logger;

        public GetManagedCapTableConfigHandler(
            IManagedCapTableService managedCapTableService,
            ILogger<GetManagedCapTableConfigHandler> logger)
        {
            _managedCapTableService = managedCapTableService ?? throw new ArgumentNullException(nameof(managedCapTableService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Handles the get managed cap table configuration query
        /// </summary>
        /// <param name="request">The configuration query</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the operation</returns>
        public async Task<GetManagedCapTableConfigResult> Handle(GetManagedCapTableConfigQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting managed cap table configuration for account: {ManagedAccountId}, module: {ModuleName}", 
                    request.ManagedAccountId, request.ModuleName);

                var configData = await _managedCapTableService.GetCapTableConfigAsync(request.ManagedAccountId, request.ModuleName);

                if (configData != null)
                {
                    _logger.LogInformation("Successfully retrieved managed cap table configuration for account: {ManagedAccountId}", 
                        request.ManagedAccountId);
                    return GetManagedCapTableConfigResult.Success(configData);
                }

                _logger.LogWarning("No managed cap table configuration found for account: {ManagedAccountId}, module: {ModuleName}", 
                    request.ManagedAccountId, request.ModuleName);
                return GetManagedCapTableConfigResult.Failure($"No cap table configuration found for managed account {request.ManagedAccountId} and module {request.ModuleName}");
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid arguments for managed cap table configuration query: {ManagedAccountId}, {ModuleName}", 
                    request.ManagedAccountId, request.ModuleName);
                return GetManagedCapTableConfigResult.Failure(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting managed cap table configuration for account: {ManagedAccountId}, module: {ModuleName}", 
                    request.ManagedAccountId, request.ModuleName);
                return GetManagedCapTableConfigResult.Failure("An unexpected error occurred while retrieving the cap table configuration");
            }
        }
    }
}
