using System;
using ManagedAccounts.Models.Results;
using MediatR;

namespace ManagedAccounts.Models.Queries
{
    /// <summary>
    /// Query for getting managed account cap table configuration
    /// </summary>
    public class GetManagedCapTableConfigQuery : IRequest<GetManagedCapTableConfigResult>
    {
        /// <summary>
        /// The ID of the managed account
        /// </summary>
        public Guid ManagedAccountId { get; set; }

        /// <summary>
        /// The module name for filtering configuration
        /// </summary>
        public required string ModuleName { get; set; }
    }
}
