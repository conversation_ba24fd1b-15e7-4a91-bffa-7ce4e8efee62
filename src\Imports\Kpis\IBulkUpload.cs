using System.Collections.Generic;
using System.Threading.Tasks;
using Contract.BulkUpload;
using Contract.Common;
using Contract.Utility;

namespace Imports.Kpi
{
    public interface IBulkUpload
    {
        Task<List<Status>> ProcessKpi(BulkUploadDataModel bulkUploadDataModel);
        Task<List<Status>> ProcessCapTableKpi(BulkUploadDataModel bulkUploadDataModel, bool isOtherCapTable = false);
        Task<List<Status>> ProcessFinancialKpis(BulkUploadDataModel bulkUploadDataModel, List<KpiModuleType> moduleTypes);
        Task<List<Status>> ProcessFundKpi(BulkUploadDataModel bulkUploadDataModel);
        Task<List<Status>> ProcessManagedCapTableKpi(BulkUploadDataModel bulkUploadDataModel, string moduleName);

    }
}